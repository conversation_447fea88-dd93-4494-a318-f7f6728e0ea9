    <!--Start Main Tab Content-->
	
    <div class="main-event-tab flow-x-hidden">
        <div class="row event_tab mr-0 ml-0">
            <div class="col-lg-12 pl-0 pr-0">

                <ul class="nav nav-pills innerpage-shadow" id="pills-tab" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="pills-home-tab" data-toggle="pill" href="#pills-home" role="tab" aria-controls="pills-home" aria-selected="true">
                            Event
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="pills-participant-tab" data-toggle="pill" href="#pills-participant" role="tab" aria-controls="pills-participant" aria-selected="false">
                            Participants
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="pills-profile-tab" data-toggle="pill" href="#pills-profile" role="tab" aria-controls="pills-profile" aria-selected="false">
                            Organizer
                        </a>
                    </li>
                    <!--<li class="nav-item">
                        <a class="nav-link" id="pills-contact-tab" data-toggle="pill" href="#pills-contact" role="tab" aria-controls="pills-contact" aria-selected="false">
                            Sponsors
                        </a>
                    </li>-->

                </ul>
                <div class="tab-content" id="pills-tabContent">

                    <!--Start Event section-->
                    <div class="tab-pane fade show active in" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab">
                        <!--Start Main Image slider section-->
                        <section class="event_main_imag">
                            <div class="container-fluid">
                                <div class="row">
                                    <div class="col-lg-12 pl-0 pr-0">
                                        <div id="carouselExampleIndicators" class="carousel slide" data-ride="carousel">
                                            <div class="carousel-inner">
                                                <?php
                                                   $featuredImageArray = explode(',', $events['events_featured_image']);
                                                    if(sizeof($featuredImageArray) > 1){
                                                    for ($i = $j; $i < $arraySize; $i++) {
                                                        if ($featuredImageArray[$i] != null) {
                                                    ?>

                                                            <div class="carousel-item <?php if ($i == $j) {
                                                                                            echo "active";
                                                                                        } ?>">
                                                                <img class="d-block w-100 " src="<?php echo $featuredImageArray[$i]; ?>" alt="First slide" onerror="this.onerror=null; this.src='<?php echo base_url('assets/images/event/noimage.jpg') ?>'">
                                                                <div class="carousel-caption1">
                                                                    <div class="main_slider_title">
                                                                        <div class="row gradient-bg pt10">
                                                                            <div class="col-6 txt_left pl-5 pad9">
                                                                                <a href="#"><img src="<?php echo base_url('assets/images/') ?>/event/back-arrow.png" class="img-fluid icon-resize" /></a>
                                                                            </div>
                                                                            <div class="col-6 txt-right pr-5 pad9">
                                                                                <a href="#"><img src="<?php echo base_url('assets/images/') ?>/event/share.png" class="img-fluid icon-resize" /></a>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                    <?php
                                                        }
                                                    }
                                                }
                                                if($events['events_featured_image'] == ""  || sizeof($featuredImageArray) == 1){
                                                ?>
                                                        <div class="carousel-item active">
                                                            <img class="d-block w-100 " src="<?php echo base_url('assets/images/event/noimage.jpg') ?>" alt="First slide">
                                                            <div class="carousel-caption1">
                                                                <div class="main_slider_title">
                                                                    <div class="row gradient-bg pt10">
                                                                        <div class="col-6 txt_left pl-5 pad9">
                                                                            <a href="#"><img src="<?php echo base_url('assets/images/') ?>/event/back-arrow.png" class="img-fluid icon-resize" /></a>
                                                                        </div>
                                                                        <div class="col-6 txt-right pr-5 pad9">
                                                                            <a href="#"><img src="<?php echo base_url('assets/images/') ?>/event/share.png" class="img-fluid icon-resize" /></a>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>


                                                <?php
                                                }

                                                ?>

                                            </div>

                                            <ol class="carousel-indicators">
                                                <?php
                                                for ($i = 1; $i < sizeof($featuredImageArray); $i++) {
                                                ?>
                                                    <li data-target="#carouselExampleIndicators" data-slide-to="0" <?php if ($i == 1) {
                                                                                                                        echo 'class="active"';
                                                                                                                    } ?>></li>
                                                <?php
                                                }
                                                ?>
                                            </ol>
                                        </div>

                                        <!--Start Date Section-->
                                        <div class="date-section">
                                            <?php $date = date_create($events['events_date']); ?>
                                            <p class="mb-0"><?php echo  date_format($date, "M"); ?></p>
                                            <p class="mb-0"><?php echo  date_format($date, "d"); ?></p>
                                        </div>
                                        <!--End Date Section-->

                                    </div>
                                </div>
                            </div>
                        </section>
                        <!--End Main Image slider section-->

                        <!--Start Event content section-->
                        <div class="container-fluid txt-black">
                            <div class="row">
                                <div class="col-lg-9 pad-lft-rgt-15">

                                    <!--Start Event Description section-->
                                    <div class="pad-lft1-rgt1-0">
                                        <div class="mb-5">
                                            <p class="event-main-title mt-4">
                                                <?php echo $events['events_name']; ?>
                                            </p>
                                            <div class="content-txt">
                                                <p><b class="fw500">Description</b></p>

                                                <?php echo $events['events_description']; ?>
                                                <span id="dots"></span>
                                                <span id="more">
                                                    <?php echo $events['events_description']; ?>
                                                </span>
                                            </div>
                                            <a class="read-more" onClick="myFunction()" id="myBtn1">
                                                <img src="<?php echo base_url('assets/images/') ?>/event/down-arrow.png" class="img-fluid" />
                                            </a>
                                        </div>
                                    </div>
                                    <!--End Event Description section-->


                                    <!--Start categort tab section-->
                                    <div class="event-cat-tab">
                                        <div class="row event_tab mt-3">
                                            <div class="col-lg-12">

                                                <ul class="nav nav-pills flow-x-hidden" id="pills-tab" role="tablist">
                                                    <li class="nav-item  mob-bdr-rgt">
                                                        <a class="nav-link active mob-bdr-lft-0" id="pills-home1-tab" data-toggle="pill" href="#pills-home1" role="tab" aria-controls="pills-home1" aria-selected="true">
                                                            <i class="motoxticket mr-2"></i><br /><span>Entry Fee</span>
                                                        </a>
                                                    </li>
                                                    <li class="nav-item  mob-bdr-rgt">
                                                        <a class="nav-link" id="pills-profile1-tab" data-toggle="pill" href="#pills-profile1" role="tab" aria-controls="pills-profile1" aria-selected="false">
                                                            <i class="motoxlocation mr-2"></i> <br /><span>Venue</span>
                                                        </a>
                                                    </li>
                                                    <li class="nav-item bdr-rgt">
                                                        <a class="nav-link" id="pills-contact1-tab" data-toggle="pill" href="#pills-contact1" role="tab" aria-controls="pills-contact1" aria-selected="false">
                                                            <i class="motoxprizemoney mr-2"></i> <br /><span>Prize </span>
                                                        </a>
                                                    </li>
                                                </ul>

                                                <div class="tab-content border pb-1 border-bottom-0 " id="pills-tabContent">

                                                    <!--Start Entry fee section-->
                                                    <div class="tab-pane show active " id="pills-home1" role="tabpanel" aria-labelledby="pills-home1-tab">

                                                        <!--Start Category Section-->
                                                        <div class="categories">
                                                            <table class="gfg1 w-100">
                                                                <tr>
                                                                    <th width="70%" class="pl-3">Category Name</th>
                                                                    <th width="30%" class="centre">Entry Fee</th>
                                                                </tr>
                                                            </table>
                                                            <table class="gfg w-100">
                                                                <?php
                                                                $categoryIds = '';
                                                                foreach ($events['categories'] as $category) {
                                                                ?>
                                                                    <tr>
                                                                        <td width="70%" class="geeks pl-3">
                                                                            <?php echo $category['categories_name']; ?>
                                                                        </td>
																		<?php
																		if($category['categories_rate'] == "0.00")
																		{
																			$categoryPrice="FREE";
																		}
																		else
																		{
																		    $categoryPrice=$category['categories_rate'] . " INR";
																		}
																		?>
                                                                        <td width="30%" class="centre" id="category_<?php echo $category['categories_id']; ?>"><?php echo $categoryPrice;?></td>
                                                                    </tr>
                                                                <?php
                                                                $categoryIds .= $category['categories_id'] . ',';
                                                                }
                                                                ?>
                                                            </table>
                                                        </div>
                                                        <!--End Category Section-->

                                                        <!--Start Get Enry Form-->


                                                        <!--End Get Enry Form-->

                                                    </div>
                                                    <!--End Entry fee section-->

                                                    <!--Start Venue section-->
                                                    <div class="tab-pane fade" id="pills-profile1" role="tabpanel" aria-labelledby="pills-profile1-tab">
                                                        <div class="venue_cont">
                                                            <div class="venue_head">
                                                                <h3>Venue : <?php echo $events['events_venue']; ?></h3>
                                                            </div>
                                                            <div class="venue_map">
                                                                <!--Google map-->
                                                                <!--<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3929.2368744828145!2d76.2985343147942!3d9.997282192853033!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3b080d166d3a98e3%3A0xc8b2f4d8eae5cacd!2sJawaharlal%20Nehru%20Stadium!5e0!3m2!1sen!2sin!4v1572159769784!5m2!1sen!2sin" width="100%" height="450" frameborder="0" style="border:0;" allowfullscreen=""></iframe>-->
                                                                <iframe src="https://maps.google.com/maps?q=<?php echo $events['events_venue']; ?>&output=embed" width="100%" height="450" frameborder="0" style="border:0;" allowfullscreen=""></iframe>

                                                                <!--Google Maps-->
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <!--End Venue section-->

                                                    <!--Start Prize section-->
                                                    <div class="tab-pane fade" id="pills-contact1" role="tabpanel" aria-labelledby="pills-contact1-tab">

                                                        <div class="prize_cont">




                                                            <div class="accordion" id="accordionExample_1">
                                                                <?php
                                                                $cnt = 1;
																/*echo "<pre>";
																print_r($vehicles);
																exit;
																*/
																
																/*echo "<pre>";
																print_r($orderedCategoryLists);
																exit;*/
                                                                foreach ($orderedCategoryLists as $orderedCategoryList) {
																
																/* echo "categories id = " .  $orderedCategoryList['categories_id']; 
																 echo  "categories name = " . $orderedCategoryList['categories_name'];*/
																 
																
																 

                                                                ?>

                                                                    <div class="card">
                                                                        <div class="card-header" id="headingOne">
                                                                            <h2 class="mb-0 border-bottom-0">
                                                                                <button class="btn font-size14" type="button" data-toggle="collapse" data-target="#collapse<?php echo $orderedCategoryList['categories_id']; ?>" aria-expanded="<?php if ($cnt == 1) {
                                                                                                                                                                                                                                                    echo "true";
                                                                                                                                                                                                                                                } else {
                                                                                                                                                                                                                                                    echo "false";
                                                                                                                                                                                                                                                } ?>" aria-controls="collapse<?php echo $orderedCategoryList['categories_id']; ?>">
                                                                                    <i class="fa fa-trophy"></i> <?php echo $orderedCategoryList['categories_name']; ?>
                                                                                </button>
                                                                            </h2>
                                                                        </div>

                                                                        <div id="collapse<?php echo $orderedCategoryList['categories_id']; ?>" class="<?php if ($cnt != 1) {
                                                                                                                                                            echo "collapse";
                                                                                                                                                        } else {
                                                                                                                                                            echo "collapse show";
                                                                                                                                                        } ?>" aria-labelledby="heading<?php echo $orderedCategoryList['categories_id']; ?>" data-parent="#accordionExample_1">
                                                                            <div class="card-body">

                                                                                <!--Start Prize money Section-->
                                                                                <div class="prize_table">
                                                                                    <?php
                                                                                    $pricesObj = json_decode($orderedCategoryList['categories_prices']);
                                                                                    ?>
                                                                                    <table class="gfg w-100">
                                                                                        <?php
                                                                                        foreach($pricesObj as $key => $price) {

                                                                                        ?>

                                                                                            <tr>
                                                                                                <td width="70%" class="geeks pl-3"><?php echo $key; ?> Prize</td>
                                                                                                <td width="30%" class="centre"><?php echo $price; ?></td>
                                                                                            </tr>
                                                                                        <?php
                                                                                        }
                                                                                        ?>

                                                                                    </table>
                                                                                </div>
                                                                                <!--End  Prize money Section-->

                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                <?php
                                                                $cnt++;
                                                                }
                                                                ?>
																
																
																
                                                            </div>

                                                        </div>

                                                    </div>
                                                    <!--End Prize section-->
                                                </div>


                                            </div>
                                        </div>
                                    </div>
                                    <!--End categort tab section-->


                                    <!--Start Entry Form section-->
                                    <div class="get-entry-form border-entry-form pb-2 footer-margin-top">

                                        <div class="entryform">
                                            <div class="entryform_head">Entry Form</div>
                                            <div id="entryform_cont" style="z-index:0;">


                                                <!--Start Registration Message-->
                                                <div class="reg_msg pad40" style="display:none;">
                                                    <div class="pass_circle"><img src="<?php echo base_url('assets/images/') ?>/edit_icon.png" /></div>
                                                    <p>You may please complete the registration process to access the Entry Form</p>
                                                    <a href="sign_up.html" name="" class="btn btn-primary btn_next next mt-2">Edit Registration</a>
                                                </div>
                                                <!--End Registration Message-->

                                                <!--Start Success Message-->
                                                <div class="success_msg pad40" style="display:none;">
                                                    <div class="pass_circle"><i class="fa fa-thumbs-o-up"></i></div>
                                                    <h1>Congratulations !</h1>
                                                    <p>You have successfully completed the Entry Registration</p>
                                                    <a href="#" name="" class="btn btn-primary btn_next next mt-2">View Entry Details</a>
                                                </div>
                                                <!--End Success Message-->



                                                <!--START form -->
                                                <?php echo form_open('events/add-participants-to-events', array(
                                                    'class' => 'form form-horizontal', 'enctype' => 'multipart/form-data', 'method' => 'post', 'id' => 'msform', 'name' => 'confirm-form'
                                                )) ?>
												
											
                                                <input type="hidden" value="<?php echo $events_id; ?>" id="events_id" name="events_id" />
                                                <input type="hidden" value="<?php echo $participants_id; ?>" id="participants_id" name="participants_id" />
                                                <input type="hidden" value="<?php echo $participants['email']; ?>" id="participants_email" name="participants_email" />
                                                <input type="hidden" name="phone" value="<?php echo $participants['phone']; ?>">
                                                <input type="hidden" name="buyer_name" value="<?php echo $participants['fname'] . ' ' . $participants['lname']; ?>">
                                                <input type="hidden" name="categoryIds" id="categoryIds" value="<?php echo $categoryIds; ?>" />
                                                <input type="hidden" value="" id="category_cnt" name="category_cnt" required />
                                                <input type="hidden" id="participants_team_name" name="participants_team_name" required />
                                                <input type="hidden" value="" id="payment_mode" name="payment_mode" required />
                                                <input type="hidden" value="" id="upiTransactionId" name="upiTransactionId" required />
 
                                                <?php if ($events['events_is_fmsci'] == 1) {
                                                ?>
                                                    <div class="form-padding">
                                                        <h1 class="mob-head-left-25">FMSCI Details</h1>

                                                        <div class="form-group mob-head-left-25">
                                                            <div class="row">
                                                                <div class="col-lg-12 col-xs-12">
                                                                    <!--<input type="text" class="form-control input_bdr2 form-input-border" id="exampleInputPassword1 " placeholder="FMSCI License No" required >-->
                                                                    <input type="text" value="<?php echo $participants['fmsci_license_number']; ?>" class="form-control input_bdr2 form-input-border " id="fmsciLicense" name="fmsciLicense" placeholder="FMSCI License No" required>

                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="form-group">
                                                            <div class="row">
                                                                <div class="col-lg-12 col-xs-12">
                                                                    <small id="emailHelp" class="form-text text-muted txt_left_small normaltxt">
                                                                        <i class="star">*</i>
                                                                        If you don't have this number, Please check the following link <a href="http://licence.fmsci.co.in" target="_blank" class="blacktxt">http://licence.fmsci.co.in</a>
                                                                    </small>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="form-divide-shadow"></div>
                                                <?php
                                                }
                                                ?>

                                                <!-- PAN Card Details Section -->
                                                <?php if ($events['events_is_pan_card'] == 1) { ?>
                                                    <div class="form-padding">
                                                        <h1 class="mob-head-left-25">PAN Card Details</h1>

                                                        <div class="form-group mob-head-left-25">
                                                            <div class="row">
                                                                <div class="col-lg-12 col-xs-12">
                                                                    <input type="text" class="form-control input_bdr2 form-input-border" id="pan_card_number" name="pan_card_number" placeholder="PAN Card Number" required>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="form-group mob-head-left-25">
                                                            <div class="row">
                                                                <div class="col-lg-12 col-xs-12">
                                                                    <input type="text" class="form-control input_bdr2 form-input-border" id="pan_card_holder_name" name="pan_card_holder_name" placeholder="PAN Card Holder Name" required>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="form-divide-shadow"></div>
                                                <?php } ?>

                                                <!-- PA Policy Details Section -->
                                                <?php if ($events['events_is_pa_policy'] == 1) { ?>
                                                    <div class="form-padding">
                                                        <h1 class="mob-head-left-25">PA Policy Details</h1>

                                                        <div class="form-group mob-head-left-25">
                                                            <div class="row">
                                                                <div class="col-lg-12 col-xs-12">
                                                                    <input type="text" class="form-control input_bdr2 form-input-border" id="pa_policy_number" name="pa_policy_number" placeholder="PA Policy Number" required>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="form-group mob-head-left-25">
                                                            <div class="row">
                                                                <div class="col-lg-12 col-xs-12">
                                                                    <input type="text" class="form-control input_bdr2 form-input-border" id="pa_policy_holder_name" name="pa_policy_holder_name" placeholder="PA Policy Holder Name" required>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="form-group mob-head-left-25">
                                                            <div class="row">
                                                                <div class="col-lg-12 col-xs-12">
                                                                    <input type="date" class="form-control input_bdr2 form-input-border" id="pa_policy_expiry_date" name="pa_policy_expiry_date" placeholder="PA Policy Expiry Date" required>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="form-group mob-head-left-25">
                                                            <div class="row">
                                                                <div class="col-lg-12 col-xs-12">
                                                                    <input type="text" class="form-control input_bdr2 form-input-border" id="pa_policy_company" name="pa_policy_company" placeholder="PA Policy Insurance Company" required>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="form-divide-shadow"></div>
                                                <?php } ?>

                                                <div id="vehicle-details-main-div" class="form-padding <?php if ($participants['fmsci_license_number'] == "" && $events['events_is_fmsci'] == 1) {
                                                                                                            echo 'hide-vehicle';
                                                                                                        } ?>">

                                                    <h1 class="pt-4 mob-head-left-25">Vehicle Details</h1>

                                                    <!--Start vehicle details form -->
                                                    <div class="veh_accord">
                                                        <div class="accordion" id="accordionExample">
                                                            <?php
                                                            $catTypeId = 0;
                                                            $i = 0;
															$count=0;
														//	echo "count = " . count($orderedCategoryLists);
                                                            foreach($orderedCategoryLists as $orderedCategoryList) {
                                                                if ($orderedCategoryList['category_type_id'] != $catTypeId) {
                                                                    $catTypeId = $orderedCategoryList['category_type_id'];
																	
																//	echo "cat type id = " . $catTypeId;
                                                            ?>

                                                                    <!-- Start Vehicle 1 -->
                                                                    <div class="card cardbdr">
                                                                        <div class="card-header relative" id="heading_<?php echo $catTypeId; ?>">
                                                                            <h2 class="mb-0 veh_sub_title">
                                                                                <button class="btn" type="button" data-toggle="collapse" data-target="#collapse_<?php echo $catTypeId; ?>" aria-expanded="<?php if ($i > 0) {
                                                                                                                                                                                                                                                    echo "false";
																																																													
                                                                                                                                                                                                                                                } else {
                                                                                                                                                                                                                                                    echo "true";
                                                                                                                                                                                                                                                } ?>" aria-controls="collapse_<?php echo $catTypeId; ?>">
                                                                                    <?php
																					// Original string
																					$bike_vehicle_category_type_name =  $orderedCategoryList['bike_vehicle_category_type_name'];
																					// Letter to find
																					$letter = "(";
																					// Find the position of the letter
																					$position = strpos($bike_vehicle_category_type_name, $letter);
																					if($position > 0)
																					{
																					   $bike_vehicle_category_type_name = substr($bike_vehicle_category_type_name,0, $position);
																					}
																					else
																					{
																						$bike_vehicle_category_type_name = $orderedCategoryList['bike_vehicle_category_type_name'];
																					}
																					 echo $bike_vehicle_category_type_name; ?>
                                                                                </button>
                                                                            </h2>
                                                                        </div>
																		
																		<?php
																		     $count++;
																																																																				                                                                           //  echo "count ....  = " . $count;
																																																																																							 																			 $catTypeNameId="cattypeid" . $count;
																																																																																							
																		?>
																		<input type="hidden" name="<?php echo $catTypeNameId; ?>" id="<?php echo $catTypeNameId; ?>" value="<?php echo $orderedCategoryList['categories_id']; ?>" /> 
																		

                                                                        <div id="collapse_<?php echo $catTypeId; ?>" class="collapse <?php if ($i == 0) {
                                                                                                                                            echo "show";
                                                                                                                                        } ?>" aria-labelledby="headingOne" data-parent="#accordionExample">
                                                                            <div class="card-body">

                                                                                <!-- Start Vehicle Form-->

                                                                                <div class="form-group mb-4">
                                                                                    <div class="row">
                                                                                        <div class="col-lg-12 col-xs-12">
                                                                                            <!--<input type="text" class="form-control input_bdr2 form-input-border" id="exampleInputPassword1 " placeholder="Your Classes to ride" required >-->

                                                                                            <select name="categories_<?php echo $orderedCategoryList['categories_id']; ?>[]" id="categories_<?php echo $orderedCategoryList['categories_id']; ?>" class="form-control selectpicker  input_bdr2 form-input-border categorySelect" multiple data-live-search="true" <?php if ($i == 0) { echo 'required';} ?>>
                                                                                                <?php
                                                                                                foreach ($orderedCategoryLists as $orderedCategoryListExtra) {
                                                                                                    if ($orderedCategoryListExtra['category_type_id'] == $catTypeId) {

                                                                                                ?>
                                                                                                        <option value="<?php echo $orderedCategoryListExtra['categories_id']; ?>"><?php echo $orderedCategoryListExtra['categories_name']; ?></option>
                                                                                                <?php
                                                                                                    }
                                                                                                }
                                                                                                ?>
                                                                                            </select>


                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                                <?php
                                                                                if ($orderedCategoryList['categories_id'] == 331) {
                                                                                    $display = 'style="display:none;"';
                                                                                    $val = 'NA';
                                                                                } else {
                                                                                    $display = '';
                                                                                    $val = '';
                                                                                }
                                                                                ?>

                                                                                <div class="form-group mb-4">
                                                                                    <div class="row">
                                                                                        <div class="col-lg-12 col-xs-12">
                                                                                            <!--<input type="text" class="form-control input_bdr2 form-input-border" id="exampleInputPassword1 " placeholder="Register Number" required >-->
                                                                                            <input type="text" value="<?php echo $val; ?>" class="form-control input_bdr2 form-input-border chkValidation vehileSuggestion" style="position:relative;" name="regNumber_<?php echo $orderedCategoryList['categories_id']; ?>" id="regNumber_<?php echo $orderedCategoryList['categories_id']; ?>" placeholder="Register Number Eg: DL 01 DK 1234" data-id="<?php echo $orderedCategoryList['categories_id']; ?>" autocomplete="off" <?php if ($i == 0) {
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        echo 'required';
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    } ?>>
                                                                                            <ul class="suggestionList" id="suggestList_<?php echo $orderedCategoryList['categories_id']; ?>" style="display: none;">

                                                                                            </ul>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>

                                                                                <div class="form-group mb-4">
                                                                                    <div class="row">
                                                                                        <div class="col-lg-12 col-xs-12">
                                                                                            <select name="make_<?php echo $orderedCategoryList['categories_id']; ?>" data-category-type-id="<?php echo $orderedCategoryList['category_type_id']; ?>" id="make_<?php echo $orderedCategoryList['categories_id']; ?>" class="form-control input_bdr selectbox1 form-input-border make chkValidation" <?php if ($i == 0) {
                                                                                                                                                                                                                                                                                                                                                                                                        echo 'required';
                                                                                                                                                                                                                                                                                                                                                                                                    } ?>>
                                                                                                <option value="">Select Vehicle Name</option>


                                                                                                <?php
																							
																								
                                                                                                /*if ($orderedCategoryList['category_type_id'] == 3) {
                                                                                                    foreach ($vehicles as $vehicle) {
                                                                                                        if ($vehicle['vehicle_id'] == 8) {
                                                                                                ?>
                                                                                                            <option value="<?php echo $vehicle['vehicle_id']; ?>"><?php echo $vehicle['vehicle_name']; ?></option>
                                                                                                        <?php
                                                                                                        }
                                                                                                    }
                                                                                                } else {*/
																								
                                                                                                    foreach ($vehicles as $vehicle) {
																									    
																										if($vehicle['vehicle_cateogry_type_id']==$orderedCategoryList['bike_vehicle_category_type_id'])
																										{
																									
                                                                                                        ?>
                                                                                                        <option value="<?php echo $vehicle['vehicle_id']; ?>"><?php echo $vehicle['vehicle_name']; ?></option>
                                                                                                <?php
																										}
                                                                                                    }
                                                                                            //    }
                                                                                                ?>
																								
																								
                                                                                            </select>

                                                                                            <!--<select class="form-control input_bdr selectbox1  form-input-border" id="exampleFormControlSelect1 ">
                                                                                <option value="">Select Vehicle Name</option>
                                                                                <option value="Suzuki">Suzuki</option>       
                                                                                <option value="Honda">Honda</option>
                                                                                <option value="Bajaj">Bajaj</option>
                                                                                <option value="Yamaha">Yamaha</option>
                                                                                <option value="TVS">TVS</option>
                                                                            </select>-->
                                                                                        </div>
                                                                                    </div>
                                                                                </div>

                                                                                <div class="form-group mb-4">
                                                                                    <div class="row">
                                                                                        <div class="col-lg-12 col-xs-12">
                                                                                            <!--<select class="form-control input_bdr selectbox1  form-input-border" id="exampleFormControlSelect1 ">
                                                                                <option value=""><i  style="color:#ccc;">Select Vehicle Model</i></option>
                                                                                <option value="Suzuki Access">Suzuki Access</option>       
                                                                                <option value="Suzuki Gixxer">Suzuki Gixxer</option>
                                                                                <option value="Suzuki RM Z450">Suzuki RM Z450</option>
                                                                                <option value="Suzuki V Strom 1000">Suzuki V Strom 1000</option>
                                                                                <option value="Suzuki RM Z250">Suzuki RM Z250</option>
                                                                            </select>-->

                                                                                            <select name="model_<?php echo $orderedCategoryList['categories_id']; ?>" id="model_<?php echo $orderedCategoryList['categories_id']; ?>" class="form-control input_bdr form-input-border selectbox1 chkValidation" <?php if ($i == 0) {
                                                                                                                                                                                                                                                                                                                    echo 'required';
                                                                                                                                                                                                                                                                                                                } ?>>
                                                                                                <option value="">Select Model</option>

                                                                                            </select>


                                                                                        </div>
                                                                                    </div>
                                                                                </div>

                                                                                <div class="form-group mb-4">
                                                                                    <div class="row">
                                                                                        <div class="col-lg-12 col-xs-12">
                                                                                            <!--<input type="text" class="form-control input_bdr2 form-input-border" id="exampleInputPassword1 " placeholder="Chassis Number" required >-->
                                                                                            <input type="text" value="<?php echo $val; ?>" class="form-control input_bdr2 form-input-border chkValidation" name="chassisNumber_<?php echo $orderedCategoryList['categories_id']; ?>" id="chassisNumber_<?php echo $orderedCategoryList['categories_id']; ?>" placeholder="Chassis Number" <?php if ($i == 0) {
                                                                                                                                                                                                                                                                                                                                                                                                echo 'required';
                                                                                                                                                                                                                                                                                                                                                                                            } ?>>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>

                                                                                <div class="form-group mb-4">
                                                                                    <div class="row">
                                                                                        <div class="col-lg-12 col-xs-12">
                                                                                            <!--<input type="text" class="form-control input_bdr2 form-input-border" id="exampleInputPassword1 " placeholder="Engine Number" required >-->
                                                                                            <input type="text" value="<?php echo $val; ?>" class="form-control input_bdr2 form-input-border chkValidation" name="engineNumber_<?php echo $orderedCategoryList['categories_id']; ?>" id="engineNumber_<?php echo $orderedCategoryList['categories_id']; ?>" placeholder="Engine Number" <?php if ($i == 0) {
                                                                                                                                                                                                                                                                                                                                                                                            echo 'required';
                                                                                                                                                                                                                                                                                                                                                                                        } ?>>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>

                                                                                <div class="form-group mb-4">
                                                                                    <div class="row">
                                                                                        <div class="col-lg-9 col-9">
                                                                                            <!--<input type="text" class="form-control input_bdr2 form-input-border"  id="exampleInputPassword1"  placeholder="Competition No" required disabled >-->
                                                                                            <input type="text" name="participantsCode_<?php echo $orderedCategoryList['categories_id']; ?>" id="participantsCode_<?php echo $orderedCategoryList['categories_id']; ?>" class="form-control input_bdr2 form-input-border chkValidation readonly" data-toggle="modal" data-target="#myModal_<?php echo $orderedCategoryList['categories_id']; ?>" placeholder="Competition No" <?php if ($i == 0) {
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    echo 'required';
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                } ?>>
                                                                                        
                                                                                        <input type="hidden" name="participantsCode_actual_<?php echo $orderedCategoryList['categories_id']; ?>" id="participantsCode_actual_<?php echo $orderedCategoryList['categories_id']; ?>" class="form-control input_bdr2 chkValidation"  placeholder="" <?php if($i==0) {echo 'required';} ?> >
                                                                                    </div>
                                                                                        <div class="col-lg-3 col-3">
                                                                                            <a type="button" href="javascript:void(0);" name="next" class="btn btn-primary btn_color next btn_left comp-no-btn" data-toggle="modal" data-target="#myModal_<?php echo $orderedCategoryList['categories_id']; ?>" data-backdrop="static">Click here</a>
                                                                                        </div>
                                                                                        <input type="hidden" class="form-control input_bdr2" name="prevCompetition_<?php echo $orderedCategoryList['categories_id']; ?>" id="prevCompetition_<?php echo $orderedCategoryList['categories_id']; ?>" placeholder="">
                                                                                    </div>
                                                                                </div>
                                                                                <input type="text" id="amount_<?php echo $orderedCategoryList['categories_id']; ?>" class="form-control input_bdr2 amountSelect" placeholder="Amount" disabled>

                                                                                <!-- End Vehicle Form-->
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                            <?php
                                                                }
                                                                $i++;
                                                            }
                                                            ?>
                                                            <!-- End Vehicle 1 -->
                                                        </div>
                                                    </div>

                                                    <!--End vehicle details form -->
                                                </div>

                                                <!--Start Payment button section -->
                                                <div class="payment-btn-section">
                                                    <div class="row">
                                                        <div class="col-lg-6 col-md-6 col-sm-6 col-6">
                                                            <span class="rupees rupees-disabled">
                                                                &#8377
                                                                <span id="grandTotalSpan" class="grandTotalSpan">0</span>
                                                                <input type="hidden" id="grandTotal">
                                                            </span>
                                                        </div>
														
														
														
                               
														
                                                        <div class="col-lg-6 col-md-6 col-sm-6 col-6 pt-2" id="dvpayment" style="display:block">
                                                            <a href="javascript:void(0);" type="" name="next" class="btn btn-primary btn_color next btn_left color-white payment-button  float-right payment-btn-disabled" id="btnpayment">NEXT</a>
                                                        </div>
                                                    </div>

                                                </div>
                                                <!--End Payment button section -->
												
												<input type="hidden" value="<?php echo $count; ?>" id="count" name="count" />

                                                <?php echo form_close(); ?>

                                                <!--END form -->


                                            </div>
                                        </div>

                                    </div>
                                    <!--End Entry Form section-->

                                </div>
                                <div class="col-lg-3 pt-4 mob-hide">
                                    <!--<img src="<?php // echo base_url('assets/images/') ?>/advt/1.jpg" class="img-fluid advt-section" />
                                    <img src="<?php // echo base_url('assets/images/') ?>/advt/1.jpg" class="img-fluid advt-section" />-->

   

                                </div>
                            </div>
                        </div>

                        <!--End Event content section-->




                    </div>
                    <!--End Event section-->

                    <!--Start Organizer section-->

                    <div class="tab-pane fade" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab">
                        <div class="organizer-section">
                            <!--Start Organizer Content-->
                            <div class="row txt-black">
                                <div class="col-lg-9">
                                    <div class="organizer bdr_rgt border-bottom">
                                        <div class="organizer_head">
                                            <span><i class="fa fa-align-left"></i>Organizer</span>
                                        </div>
                                        <div class="org_img_cont">
										   <?php
										      if($events['display_name'] != "")
											  {
										      	 $display_name=$events['display_name'];
											  }
											  else
											  {
											 	 $display_name=$events['users_name'];
											  }
										   ?>
                                           <h3><?php echo $display_name;?></h3>
                                        </div>
                                    </div>

                                    <!--Start Upcoming events Content-->
                                   <?php
										 $count=0;
										 $totOrganizerEventDetailsList=count($organizerEventDetails);
									?>
                                    <div class="related-events bdr_rgt p-b-0">
                                        <span class="dashboard-title">Organized Events List - <?php  echo $totOrganizerEventDetailsList; ?></span>

                                        <!-- Start Poster Row -->
                                        <div class="row upcoming-img-radius ">
										
                                            
                                         
                                          <div class="p-t-b-20 txt-justify" style="width:100%;">
													<p>
																		<style>
																					div.blueTable {
																					  border: 1px solid #1C6EA4;
																					  background-color: #EEEEEE;
																					  width: 100%;
																					  text-align: left;
																					  border-collapse: collapse;
																					}
																					.divTable.blueTable .divTableCell, .divTable.blueTable .divTableHead {
																					  border: 1px solid #AAAAAA;
																					  padding: 3px 2px;
																					}
																					.divTable.blueTable .divTableBody .divTableCell {
																					  font-size: 13px;
																					}
																					.divTable.blueTable .divTableRow:nth-child(even) {
																					  background: #D0E4F5;
																					}
																					.divTable.blueTable .divTableHeading {
																					  background: #1C6EA4;
																					  background: -moz-linear-gradient(top, #5592bb 0%, #327cad 66%, #1C6EA4 100%);
																					  background: -webkit-linear-gradient(top, #5592bb 0%, #327cad 66%, #1C6EA4 100%);
																					  background: linear-gradient(to bottom, #5592bb 0%, #327cad 66%, #1C6EA4 100%);
																					  border-bottom: 2px solid #444444;
																					}
																					.divTable.blueTable .divTableHeading .divTableHead {
																					  font-size: 15px;
																					  font-weight: bold;
																					  color: #FFFFFF;
																					  border-left: 2px solid #D0E4F5;
																					}
																					.divTable.blueTable .divTableHeading .divTableHead:first-child {
																					  border-left: none;
																					}
																					
																					.blueTable .tableFootStyle {
																					  font-size: 14px;
																					  font-weight: bold;
																					  color: #FFFFFF;
																					  background: #D0E4F5;
																					  background: -moz-linear-gradient(top, #dcebf7 0%, #d4e6f6 66%, #D0E4F5 100%);
																					  background: -webkit-linear-gradient(top, #dcebf7 0%, #d4e6f6 66%, #D0E4F5 100%);
																					  background: linear-gradient(to bottom, #dcebf7 0%, #d4e6f6 66%, #D0E4F5 100%);
																					  border-top: 2px solid #444444;
																					}
																					.blueTable .tableFootStyle {
																					  font-size: 14px;
																					}
																					.blueTable .tableFootStyle .links {
																						 text-align: right;
																					}
																					.blueTable .tableFootStyle .links a{
																					  display: inline-block;
																					  background: #1C6EA4;
																					  color: #FFFFFF;
																					  padding: 2px 8px;
																					  border-radius: 5px;
																					}
																					.blueTable.outerTableFooter {
																					  border-top: none;
																					}
																					.blueTable.outerTableFooter .tableFootStyle {
																					  padding: 3px 5px; 
																					}
																					/* DivTable.com */
																					.divTable{ display: table; }
																					.divTableRow { display: table-row; }
																					.divTableHeading { display: table-header-group;}
																					.divTableCell, .divTableHead { display: table-cell;}
																					.divTableHeading { display: table-header-group;}
																					.divTableFoot { display: table-footer-group;}
																					.divTableBody { display: table-row-group;}
																		</style>
																		
																		
																		
																		
																		<?php
																		  if($organizerEventDetails)
																		  {
																	   ?>
																					<div class="divTable blueTable">
																					<div class="divTableHeading">
																					<div class="divTableRow">
																					<div class="divTableHead">Sl No</div>
																					<div class="divTableHead">Event Name</div>
																					<div class="divTableHead">Date</div>
																				<div class="divTableHead">Venue</div>
																					</div>
																					</div>
																					<div class="divTableBody">
																					<?php
																					 foreach($organizerEventDetails as $organizerEvents)
																					   {
																					  // print_r($organizerEvents);
																							$count=$count+1;
																					?>
																							<div class="divTableRow">
																							<div class="divTableCell"><?php echo $count; ?></div>
																							<div class="divTableCell"><?php echo $organizerEvents['events_name']; ?></div>
																							<div class="divTableCell"><?php echo $organizerEvents['events_date']; ?></div>
																							<div class="divTableCell"><?php echo $organizerEvents['events_venue']; ?></div>
																							</div>
																					<?php
																					   }
																					?>
																					</div>
																					</div>
																					<br/><br/>
																			 <?php
																			  }
																			?>
													</p>
												</div>
											<span> </span>
                                        </div>
                                        <!-- End Poster Row -->
                                    </div>

                                    <!--End Upcoming events Content-->
                                </div>

                                <div class="col-lg-3 pt-4 mob-hide">
                                   <!-- <img src="<?php // echo base_url('assets/images/') ?>/advt/1.jpg" class="img-fluid advt-section" />-->
                                   <ins class="adsbygoogle"
                                        style="display:block"
                                        data-ad-client="ca-pub-1139722961154347"
                                        data-ad-slot="3180047683"
                                        data-ad-format="auto"
                                        data-full-width-responsive="true"></ins>
                                    <script>
                                        (adsbygoogle = window.adsbygoogle || []).push({});
                                    </script>    
									<?php
									   foreach($bikeEventOrganizerSidebarList as $bikeEventOrganizerSidebar)
									   {
											 echo $bikeEventOrganizerSidebar->addsImgPath;
									   }
                               		?>
                                </div>
                            </div>
                            <!--End Organizer Content-->

                        </div>
                    </div>
                    <!--End Organizer section-->

                    <!--Start Sponsors section-->
                    <div class="tab-pane fade" id="pills-contact" role="tabpanel" aria-labelledby="pills-contact-tab">
                        <!--Start Sponsors content-->
                        <div class="sponsor-section">
                            <div class="row txt-black">
                                <div class="col-lg-9">
                                    <!-- Start Sponsors -->
                                    <div class="sponsor  bdr_rgt border-bottom">
                                        <div class="organizer_head">
                                            <span><i class="fa fa-align-left"></i>Sponsors</span>
                                        </div>
                                        <div>
                                            <div id="carouselExampleIndicators1" class="carousel slide" data-ride="carousel">
                                                <ol class="carousel-indicators">
                                                    <li data-target="#carouselExampleIndicators1" data-slide-to="0" class="active"></li>
                                                    <li data-target="#carouselExampleIndicators1" data-slide-to="1"></li>
                                                </ol>
                                                <div class="carousel-inner">
                                                    <div class="carousel-item active">
                                                        <div class="row">
                                                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 pr-0">
                                                                <div class="spon_img_cont">
                                                                    <img src="<?php echo base_url('assets/images/') ?>/sponsors/3.jpg" class="img-fluid" />
                                                                </div>
                                                            </div>
                                                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
                                                                <div class="spon_img_cont">
                                                                    <img src="<?php echo base_url('assets/images/') ?>/sponsors/4.jpg" class="img-fluid" />
                                                                </div>
                                                            </div>
                                                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6  pr-0">
                                                                <div class="spon_img_cont">
                                                                    <img src="<?php echo base_url('assets/images/') ?>/sponsors/5.jpg" class="img-fluid" />
                                                                </div>
                                                            </div>
                                                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 ">
                                                                <div class="spon_img_cont">
                                                                    <img src="<?php echo base_url('assets/images/') ?>/sponsors/6.jpg" class="img-fluid" />
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="carousel-item">
                                                        <div class="row">
                                                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 pr-0">
                                                                <div class="spon_img_cont">
                                                                    <img src="<?php echo base_url('assets/images/') ?>/sponsors/3.jpg" class="img-fluid" />
                                                                </div>
                                                            </div>
                                                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6">
                                                                <div class="spon_img_cont">
                                                                    <img src="<?php echo base_url('assets/images/') ?>/sponsors/4.jpg" class="img-fluid" />
                                                                </div>
                                                            </div>
                                                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6  pr-0">
                                                                <div class="spon_img_cont">
                                                                    <img src="<?php echo base_url('assets/images/') ?>/sponsors/5.jpg" class="img-fluid" />
                                                                </div>
                                                            </div>
                                                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 ">
                                                                <div class="spon_img_cont">
                                                                    <img src="<?php echo base_url('assets/images/') ?>/sponsors/6.jpg" class="img-fluid" />
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- End Sponsors -->


                                    <!--Start Upcoming events Content-->
                                    <div class="related-events bdr_rgt p-b-0">
                                        <span class="dashboard-title">Sponsored Events</span>

                                        <!-- Start Poster Row -->
                                        <div class="row upcoming-img-radius">

                                            <div class="col-lg-4 col-sm-6 col-md-4 col-6 m-b-50">
                                                <a href="#" class="dashboard-button-reg buttonblue">GET ENTRY</a>
                                                <a href="event.html" style="display:block">
                                                    <img src="<?php echo base_url('assets/images/') ?>/event/poster3.jpg" alt="image" class="img-fluid" />
                                                </a>
                                                <a href="event.html" style="display:block">
                                                    <div class="dashboard-button-date">Fri, 29 Feb 2020</div>
                                                </a>
                                            </div>
                                            <div class="col-lg-4 col-sm-6 col-md-4 col-6 m-b-50">
                                                <a href="#" class="dashboard-button-reg buttonblue">GET ENTRY</a>
                                                <img src="<?php echo base_url('assets/images/') ?>/event/poster2.jpg" alt="image" class="img-fluid" />
                                                <div class="dashboard-button-date">Fri, 29 Feb 2020</div>
                                            </div>
                                            <div class="col-lg-4 col-sm-6 col-md-4 col-6 m-b-50">
                                                <a href="#" class="dashboard-button-reg buttonred">PENDING</a>
                                                <img src="<?php echo base_url('assets/images/') ?>/event/poster4.jpg" alt="image" class="img-fluid" />
                                                <div class="dashboard-button-date">Fri, 29 Feb 2020</div>
                                            </div>
                                            <div class="col-lg-4 col-sm-6 col-md-4 col-6 m-b-50">
                                                <a href="#" class="dashboard-button-reg buttongreen">CONFIRM</a>
                                                <img src="<?php echo base_url('assets/images/') ?>/event/poster5.jpg" alt="image" class="img-fluid" />
                                                <div class="dashboard-button-date">Fri, 29 Feb 2020</div>
                                            </div>
                                            <div class="col-lg-4 col-sm-6 col-md-4 col-6 m-b-50">
                                                <a href="#" class="dashboard-button-reg buttonblue">GET ENTRY</a>
                                                <img src="<?php echo base_url('assets/images/') ?>/event/poster6.jpg" alt="image" class="img-fluid" />
                                                <div class="dashboard-button-date">Fri, 29 Feb 2020</div>
                                            </div>
                                            <div class="col-lg-4 col-sm-6 col-md-4 col-6 m-b-50">
                                                <a href="#" class="dashboard-button-reg buttonred">PENDING</a>
                                                <img src="<?php echo base_url('assets/images/') ?>/event/poster7.jpg" alt="image" class="img-fluid" />
                                                <div class="dashboard-button-date">Fri, 29 Feb 2020</div>
                                            </div>
                                        </div>
                                        <!-- End Poster Row -->
                                    </div>

                                    <!--End Upcoming events Content-->

                                </div>

                                <div class="col-lg-3 pt-4 mob-hide">
                                    <img src="<?php echo base_url('assets/images/') ?>/advt/1.jpg" class="img-fluid advt-section" />
                                    <img src="<?php echo base_url('assets/images/') ?>/advt/1.jpg" class="img-fluid advt-section" />
                                </div>
                            </div>
                        </div>
                        <!--End Sponsors content-->
                    </div>
                    <!--End Sponsors section-->

                    <!--Start Participants section-->
                    <div class="tab-pane fade" id="pills-participant" role="tabpanel" aria-labelledby="pills-participant-tab">

                        <!--Start participant content-->
                        <div class="sponser-section">
                            <div class="row txt-black">
                                <div class="col-lg-9">
                                    <!-- Start participant -->
                                    <div class="participant-title">
                                        CONFIRMED PARTICIPANTS : <?php echo sizeof($eventParticipants);?>
                                    </div>
                                    <div class="bdr_rgt border-bottom participant-list">
                                        <div class="container-fluid fo-sz-14">
                                            <div class="row partici-list-title">
                                                <div class="col-lg-12">
                                                    <div class="participane-details"><strong>Participant</strong></div>
                                                    <div class="competition-no"><strong>Comp.No</strong></div>
                                                    <div class="category-listing"><strong>Category</strong></div>
                                                </div>
                                            </div>
<?php 
foreach($eventParticipants as $eventParticipant){

    $participantsEventCategoryDetails = $this->events_model->getCategoryDetailsByUserEvent($eventParticipant['events_details_participants_id'],$eventParticipant['events_details_events_id']);
    //echo '<pre';
    //echo 257846563456349563495;
    //print_r($participantsEventCategoryDetails[0]);exit;
    $categoryDivDetails = '';
    $participantsCodeArray = Array();
    foreach($participantsEventCategoryDetails as $participantsEventCategoryDetail)
    {
        $categoryDivDetails .="<div>".$participantsEventCategoryDetail['categories_name']."</div>";
        Array_push($participantsCodeArray, $participantsEventCategoryDetail['events_details_participants_code']);
    }


    $uniqueParticipantCodes = array_unique($participantsCodeArray);
    $commaSeparatedParticipantCodes = implode(', ', $uniqueParticipantCodes);
?>
                                            <!--Start Participant 1-->
                                            <div class="row partici-content-row">
                                                <div class="col-lg-12">
                                                    <div class="participane-details">
														<?php
														   if($eventParticipant['documents_name'] != "")
														   {
																 $participants_image=base_url() . "assets/uploads/participants/" . $eventParticipant['documents_name'];
														   }
														   else
														   {
																 $participants_image=base_url('assets/images/') . "participants/noimage.jpg";
														   }
														    $imgHrefPath=base_url() . "participants/view-participant-profile/" .$eventParticipant['participants_id'];
														?>
														<a href="<?php echo $imgHrefPath; ?>"><img src="<?php echo $participants_image;?>" alt="Avatar" class="right widthresize"></a>
                                                        <span><a href="<?php echo $imgHrefPath; ?>"><?php echo $eventParticipant['participants_name'];?></a></span>
                                                        <i><?php echo $eventParticipant['participants_city'];?></i>
                                                    </div>
                                                    <div class="competition-no p-l-6"><?php echo $commaSeparatedParticipantCodes;?></div>
                                                    <div class="category-listing">
                                                    <?php echo $categoryDivDetails; ?>
                                                        
                                                    </div>
                                                </div>
                                            </div>
                                            <!--END Participant 1-->
<?php
}
?>


                                        </div>
                                    </div>
									
									
									
									
									
									
									
									
									
									
									
									<div class="participant-title">
                                        UNDER VERIFICATION : <?php echo sizeof($eventPendingParticipants);?>
                                    </div>
                                    <div class="bdr_rgt border-bottom participant-list">
                                        <div class="container-fluid fo-sz-14">
                                            <div class="row partici-list-title">
                                                <div class="col-lg-12">
                                                    <div class="participane-details"><strong>Participant</strong></div>
                                                    <div class="competition-no"><strong>Comp.No</strong></div>
                                                    <div class="category-listing"><strong>Category</strong></div>
                                                </div>
                                            </div>
<?php 
foreach($eventPendingParticipants as $eventParticipant){

    $participantsEventCategoryDetails = $this->events_model->getCategoryDetailsByUserEvent($eventParticipant['events_details_participants_id'],$eventParticipant['events_details_events_id']);
    //echo '<pre';
    //echo 257846563456349563495;
    //print_r($participantsEventCategoryDetails[0]);exit;
    $categoryDivDetails = '';
    $participantsCodeArray = Array();
    foreach($participantsEventCategoryDetails as $participantsEventCategoryDetail)
    {
        $categoryDivDetails .="<div>".$participantsEventCategoryDetail['categories_name']."</div>";
        Array_push($participantsCodeArray, $participantsEventCategoryDetail['events_details_participants_code']);
    }


    $uniqueParticipantCodes = array_unique($participantsCodeArray);
    $commaSeparatedParticipantCodes = implode(', ', $uniqueParticipantCodes);
?>
                                            <!--Start Participant 1-->
                                            <div class="row partici-content-row">
                                                <div class="col-lg-12">
                                                    <div class="participane-details">
														<?php
														   if($eventParticipant['documents_name'] != "")
														   {
																 $participants_image=base_url() . "assets/uploads/participants/" . $eventParticipant['documents_name'];
														   }
														   else
														   {
																 $participants_image=base_url('assets/images/') . "participants/noimage.jpg";
														   }
														    $imgHrefPath=base_url() . "participants/view-participant-profile/" .$eventParticipant['participants_id'];
														?>
														<a href="<?php echo $imgHrefPath; ?>"><img src="<?php echo $participants_image;?>" alt="Avatar" class="right widthresize"></a>
                                                        <span><a href="<?php echo $imgHrefPath; ?>"><?php echo $eventParticipant['participants_name'];?></a></span>
                                                        <i><?php echo $eventParticipant['participants_city'];?></i>
                                                    </div>
                                                    <div class="competition-no p-l-6"><?php echo $commaSeparatedParticipantCodes;?></div>
                                                    <div class="category-listing">
                                                    <?php echo $categoryDivDetails; ?>
                                                        
                                                    </div>
                                                </div>
                                            </div>
                                            <!--END Participant 1-->
<?php
}
?>


                                        </div>
                                    </div>
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
									
                                    <!-- End Participant -->


                                    <!--Start Upcoming events Content-->
                                    <div class="related-events bdr_rgt p-b-0">
                                        <span class="dashboard-title">Sponsored Events</span><br />
<span></span>
                                        <!-- Start Poster Row -->
                                       <!-- <div class="row upcoming-img-radius">

                                            <div class="col-lg-4 col-sm-6 col-md-4 col-6 m-b-50">
                                                <a href="#" class="dashboard-button-reg buttonblue">GET ENTRY</a>
                                                <a href="event.html" style="display:block">
                                                    <img src="<?php echo base_url('assets/images/') ?>/event/poster3.jpg" alt="image" class="img-fluid" />
                                                </a>
                                                <a href="event.html" style="display:block">
                                                    <div class="dashboard-button-date">Fri, 29 Feb 2020</div>
                                                </a>
                                            </div>
                                            <div class="col-lg-4 col-sm-6 col-md-4 col-6 m-b-50">
                                                <a href="#" class="dashboard-button-reg buttonblue">GET ENTRY</a>
                                                <img src="<?php echo base_url('assets/images/') ?>/event/poster2.jpg" alt="image" class="img-fluid" />
                                                <div class="dashboard-button-date">Fri, 29 Feb 2020</div>
                                            </div>
                                            <div class="col-lg-4 col-sm-6 col-md-4 col-6 m-b-50">
                                                <a href="#" class="dashboard-button-reg buttonred">PENDING</a>
                                                <img src="<?php echo base_url('assets/images/') ?>/event/poster4.jpg" alt="image" class="img-fluid" />
                                                <div class="dashboard-button-date">Fri, 29 Feb 2020</div>
                                            </div>
                                            <div class="col-lg-4 col-sm-6 col-md-4 col-6 m-b-50">
                                                <a href="#" class="dashboard-button-reg buttongreen">CONFIRM</a>
                                                <img src="<?php echo base_url('assets/images/') ?>/event/poster5.jpg" alt="image" class="img-fluid" />
                                                <div class="dashboard-button-date">Fri, 29 Feb 2020</div>
                                            </div>
                                            <div class="col-lg-4 col-sm-6 col-md-4 col-6 m-b-50">
                                                <a href="#" class="dashboard-button-reg buttonblue">GET ENTRY</a>
                                                <img src="<?php echo base_url('assets/images/') ?>/event/poster6.jpg" alt="image" class="img-fluid" />
                                                <div class="dashboard-button-date">Fri, 29 Feb 2020</div>
                                            </div>
                                            <div class="col-lg-4 col-sm-6 col-md-4 col-6 m-b-50">
                                                <a href="#" class="dashboard-button-reg buttonred">PENDING</a>
                                                <img src="<?php echo base_url('assets/images/') ?>/event/poster7.jpg" alt="image" class="img-fluid" />
                                                <div class="dashboard-button-date">Fri, 29 Feb 2020</div>
                                            </div>
                                        </div>-->
                                        <!-- End Poster Row -->
                                    </div>

                                    <!--End Upcoming events Content-->

                                </div>

                                <div class="col-lg-3 pt-4 mob-hide">
								<?php
									   foreach($bikeEventParticipantsSidebarList as $bikeEventParticipantsSidebar)
									   {
											 echo $bikeEventParticipantsSidebar->addsImgPath;
									   }
                                ?>
                                </div>
                            </div>
                        </div>
                        <!--End participant content-->

                    </div>
                    <!--End Participants section-->
                </div>



            </div>
        </div>
    </div>
    <!--End Main Tab Content-->
    <?php
    $catTypeId = 0;
    foreach ($orderedCategoryLists as $orderedCategoryList) {
        if ($orderedCategoryList['category_type_id'] != $catTypeId) {
            $catTypeId = $orderedCategoryList['category_type_id'];
    ?>
            <!-- The Modal Competition Number -->
            <div class="modal fade" id="myModal_<?php echo $orderedCategoryList['categories_id']; ?>">
                <div class="modal-dialog dialog_margin">
                    <div class="modal-content">

                        <!-- Modal Header -->
                        <div class="modal-header">
                            <button type="button" class="close model_close" data-dismiss="modal"> </button>
                        </div>

                        <!-- Modal body -->
                        <div class="modal-body centre">
                            <h1>Select your Competition Number</h1>

                            <div class="seat_outer">


                                <div class="plane">
                                    <ol class="cabin">
                                        <li class="row row--1">
                                            <ol class="seats" type="A">
											
											
												<?php
												$competitionNoFrom=$events['events_competitionno_from'];
												$competitionNoUpTo=$events['events_competitionno_upto'];
                                                for ($j = $competitionNoFrom; $j <= $competitionNoUpTo; $j++) 
												{
                                                    if ($j < 10)
                                                        $n = '0' . $j;
                                                    else
                                                        $n = $j;
                                                    $setDisableStatus = in_array($j, $participants_code['participants_code']);

                                                ?>
											
											
											
                                             
                                                    <li class="seat">
                                                        <input type="radio" name="seat_<?php echo $orderedCategoryList['categories_id']; ?>" id="myModal_<?php echo $orderedCategoryList['categories_id']; ?>_<?php echo $n; ?>" class="<?php echo $n; ?>" data-app="<?php echo $n; ?>" value="<?php echo $j; ?>" data-actual-source="participantsCode_actual_<?php echo $orderedCategoryList['categories_id']; ?>" data-source="participantsCode_<?php echo $orderedCategoryList['categories_id']; ?>" <?php if ($setDisableStatus) {
                                                                                                                                                                                                                                                                                                                                                                                                                               echo 'disabled="disabled"';
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        } ?> />
                                                        <label for="myModal_<?php echo $orderedCategoryList['categories_id']; ?>_<?php echo $n; ?>"><?php echo $n; ?></label>
                                                    </li>
                                                <?php
                                                }
                                                ?>

                                            </ol>
                                        </li>
                                    </ol>
                                </div>





                            </div>




                        </div>

                        <!-- Modal footer -->
                        <div class="modal-footer pb30"></div>
                    </div>
                </div>
            </div>
            <!-- End Modal Competition Number -->
    <?php
        }
    }
    ?>
    <!-- The Modal payment starts here -->
    
	<style>
    .wid400{
        width: 400px;
        padding: 35px;
    }
    .pop-title{
        color: #64626c;
        font-size: 25px;
        font-family: 'Ubuntu', sans-serif;
        font-weight: 400;
    }
    .center-align{
        text-align: center;
    }
    .center-align img{
        margin-right: 20px;
    }
    .mb-25{
        margin-bottom: 25px;
    }
    .payment-listing-box{
        border: 1px solid #e1e1e1;
        height: auto;
        border-radius: 8px;
        margin-bottom: 10px;
        padding: 10px;
    }
    .payment-listing-box-ash{
        border: 1px solid #e1e1e1;
        height: auto;
        border-radius: 8px;
        margin-bottom: 10px;
        padding: 10px;
        background: #eeeded;
    }
    .payment-listing-box-ash ul{
        list-style: none;
        margin: 0px !important;
        padding: 3px 10px;
    }
    .payment-listing-box-ash ul li{
        color: black;
        font-size: 14px;
        font-weight: 400;
        float: left;
        margin-right: 10px;
        
    }
    
    .payment-listing-box ul{
        list-style: none;
        margin: 0px !important;
        padding: 3px 10px;
    }
    .payment-listing-box ul li{
        color: black;
        font-size: 14px;
        font-weight: 400;
        float: left;
        margin-right: 10px;
        
    }
    .listing-box-title{
        color: black;
        font-size: 14px;
        font-weight: 500;
        padding-left: 10px;
    }
    .listing-box-number{
        color: black;
        font-size: 14px;
        font-weight: 500;
    }
    .bdr-0{
        border: 0px !important;
    } 
    .pad-0{
        padding: 0px !important;
    }
    .upi-box{
        border: 1px solid #cbcbcb !important;
        margin: 0px !important;
        padding: 10px 22px !important;
        text-align: left !important;
    }
    .upi-box:hover{
        border: 1px solid #001DFD !important;
        background: #001dfd36 !important;
    }
    .upi-footer{
        border: 0px !important;
        margin-top: 0px !important;
        padding: 10px 0px !important;
        justify-content: center !important;
    }
    .upi-footer .btn_proceed:hover{
        font-size: 17px !important;
        border-radius: 8px !important;
    }
    .upi-close{
        height: auto;
        margin-bottom: 10px;
        opacity: 1;
        font-size: 17px;
        color: #7a8087;
        font-weight: normal;
        width: 50%;
        border: 1px solid #e1e1e1 !important;
        border-radius: 8px;
        padding: 11px !important;
        margin-top: 9px;
    }
    .upi-next-btn{
        min-width: 50% !important;
        border-radius: 8px !important;
        margin: 0px !important;
        font-size: 17px;
    }
    .m-auto{
        display: flex;
        justify-content: center;
        align-items: center;
    }
    @media (min-width: 200px) and (max-width: 400px) { 
        .pop-title{
            display: block;
        }
    }
</style>



<!-- The Modal payment starts here -->
<div class="modal fade" id="myModal_payment" data-backdrop="static" >
    <div class="modal-dialog dialog_margin m-auto">
        <div class="modal-content wid400">
            <!-- Modal Header -->
            <!--<div class="modal-header">
                <button type="button" class="close model_close" data-dismiss="modal"> </button>
            </div>  
        -->              
            <div class="modal-body  p-0">

                    <div class="row center-align">
                        <div class="col-md-12 mb-25">
                            <img src="<?php echo base_url();?>assets/images/loudspeaker-icon.png" alt="">
                            <span class="pop-title"> Are you sure ?</span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 payment-listing-box">
                            <div class="listing-box-title">Category</div>
                            <ul>
								<div id="selectedclasslist"></div>
                            </ul>
                        </div>
                    
                        <div class="col-md-12 payment-listing-box-ash">
                            <ul>
                                <li><span class="listing-box-number"> TOTAL AMOUNT:</span> </li>
                                <li><span class="listing-box-number grandTotalSpan">0 INR </span></li>
                            </ul>
                        </div>
                        
                        <div class="col-md-12 payment-listing-box bdr-0 pad-0">
                            <span class="listing-box-number"> Payment Method</span> 
										<?php
										$paymentMethodSelected=0;
										if($events['events_is_online_pay'] == 1) 
										{
												$paymentMethodSelected=2;
										?>
												<a href="javascript:void(0);" onclick="updatePaymentVal(2)" class="payment-mode-click m-b-15">
													 <input class="form-check-input mt-4 opacity-zero" type="radio" name="payment_modeModal" id="payment_mode2Modal" value="2">
													 <img src="<?php echo base_url('assets/images/') ?>payment/net.png" class="img-fluid " style="padding-left: 20px;" >
												</a>  
                          				<?php
                                        }
                                        if ($events['events_is_coe'] == 1) 
										{
												$paymentMethodSelected=1;
                                        ?>
												<a href="javascript:void(0);" onclick="updatePaymentVal(1)" class="payment-mode-click ">
													<input class="form-check-input mt-4 opacity-zero" type="radio" name="payment_modeModal" id="payment_mode1Modal" value="1">
													<img src="<?php  echo base_url('assets/images/') ?>upi-small.png" class="img-fluid " style="padding-left: 20px;" />
												</a>
										<?php
                                        }
                                        ?>

							
							
						
                        </div>
                    </div>
                    



        <!--Start slide Gpay Section-->
        <div class="row slideGpay upidSlideDiv" id="upiDiv" style="display: none;">
                <div class="col-md-12">
                    <div>
                        <div class="row p-50-60 ">
                            <div class="muli-color-bdr">
                                <div class="col-md-12 bg-white bdr-radius-20 txt-center p-15-30">
                                    <img src="<?php echo base_url('assets/images/') ?>payment/gpay.jpg" class="width-50per" />
                                    <hr class="payment-hr">
                                    <?php
                                if($events['upi_id'] != '' && $events['upi_mobile'] != '' && $events['upi_qrcode'] != '')
                                {
                                ?>
                                    <span class="gpay-title"><?php echo $events['upi_name'];?></span> 
                                    <p class="fnt-16"><?php echo $events['upi_mobile'];?></p>
                                    <img src="<?php echo $events['upi_qrcode'];?>" class="img-fluid" />
                                    <p class="fnt-18"><?php echo $events['upi_id'];?></p>
                                <?php
                                }
                                else{

                                ?>
                                    <span class="gpay-title">MotoXindia</span> 
                                    <p class="fnt-16">9447601401</p>
                                    <img src="<?php echo base_url('assets/images/') ?>payment/qr.jpg" class="img-fluid" />
                                    <p class="fnt-18">motoxindia@okicici</p>
                                <?php  
                                }
                                ?>

                                    <div class="row upid-cont">
                                        <div class="col-12">
                                            <p class="fnt-16">
                                                After making the payment. <br>
                                                Please submit your UPI Transaction Id<br>
                                                <input type="text" value="" class="form-control input_bdr2 mob-center m-t-b-10 bdr-radius-5 txt-center" id="upiTransactionIdModal" name="upiTransactionIdModal" placeholder="UPI Transaction Id" required="required">
                                                <!--<button type="submit" name="next" id="proceedPaymentButtonLoader" class="btn btn-primary btn_next next mt-2 proceedPayment upid-btn">Proceed</button>-->
                                            </p>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="icon-col-20">
                                            <img src="<?php echo base_url('assets/images/') ?>payment/gicon.png" class="img-fluid" /> 
                                        </div>
                                        <div class="icon-col-20">
                                            <img src="<?php echo base_url('assets/images/') ?>payment/paytmicon.png" class="img-fluid" /> 
                                        </div>
                                        <div class="icon-col-20">
                                            <img src="<?php echo base_url('assets/images/') ?>payment/amazone.png" class="img-fluid" /> 
                                        </div>
                                        <div class="icon-col-20">
                                            <img src="<?php echo base_url('assets/images/') ?>payment/phonepe.png" class="img-fluid" /> 
                                        </div>
                                        <div class="icon-col-20">
                                            <img src="<?php echo base_url('assets/images/') ?>payment/imobile.png" class="img-fluid" /> 
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="icon-col-50 txt-right">
                                            <img src="<?php echo base_url('assets/images/') ?>payment/BHIM.png" class="img-fluid" /> 
                                        </div>
                                        <div class="icon-col-50 txt-left">
                                            <img src="<?php echo base_url('assets/images/') ?>payment/upi-icon.png" class="img-fluid" /> 
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <!--End slide Gpay Section-->

        
        <!--Start processing Section-->
        <div class="row slideGpay" style="display: none;" >
               <div class="col-md-12">
                    <div>
                        <div class="row process-mainDiv">
                            <div class="muli-color-bdr">
                                <div class="col-md-12 bg-white bdr-radius-20 txt-center p-15-30">                                    
                                    <div class="row process-cont  bg-white">
                                        <div class="col-12 p-t-l-r-30">
                                            <h4 class="fnt-16">PAYMENT PROCESSING</h4>
                                            <hr class="payment-hr">
                                            <img src="<?php echo base_url('assets/images/') ?>payment/process.gif" class="img-fluid ani-gif-process" />

                                            <p class="fnt-16">
                                                Your Payment transaction is being processed.
                                                 It will take maximum 2 hours to get the confirmation.<br><br>
                                                <b>Thanks for your patience</b> <br>
                                                <b>Team Motoxindia</b> <br>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
               </div>
         </div>
        <!--End processing Section-->

        
        <!--Start Congratulations Section-->
        <div class="row slideGpay" style="display: none;" >
                <div class="col-md-12">
                    <div>
                        <div class="row process-mainDiv">
                            <div class="muli-color-bdr">
                                <div class="col-md-12 bg-white bdr-radius-20 p-15-30">                                    
                                    <div class="row process-cont  bg-white p-t-l-r-30">
                                        <div class="col-md-9 p-r-0">
                                            <h4 class="payment-success-head">Payment Successful</h4>
                                            <p class="fnt-16 txt-left">
                                                Your transaction has been successfully processed.
                                                Enjoy the thrilling world of Motorsport.<br>
                                                
                                                <b>17 Oct, 2020 | 3:27 pm</b> <br>
                                            </p>
                                        </div>
                                        <div class="col-md-3">
                                            <img src="<?php echo base_url('assets/images/') ?>payment/check-circle.gif" class="img-fluid" />
                                        </div>
                                    </div>                                    
                                    <div class="payment-details-table">
                                        <b>Details</b>
                                        <div class="payment-details-box">
                                            <div class="row">
                                                <div class="col-md-6 p-t-5 p-l-25">Class to ride</div>
                                                <div class="col-md-6 p-t-5 bold">Indian Bike Race, Foreign Open Bike Race</div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6 p-t-5 p-l-25">Competition No</div>
                                                <div class="col-md-6 p-t-5 bold">38, 46</div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6 p-t-5 p-l-25">Amount</div>
                                                <div class="col-md-6 p-t-5 bold">₹​ 3,000</div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6 p-t-5 p-l-25">Paid Amount</div>
                                                <div class="col-md-6 p-t-5 bold">₹​ 3,000</div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6 p-t-5 p-l-25">Transaction ID</div>
                                                <div class="col-md-6 p-t-5 bold">132DF123648</div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6 p-t-5 p-l-25">Payment Mode</div>
                                                <div class="col-md-6 p-t-5 bold">UPI</div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <span class="payment-note">Note</span> You can find detailed invoice of your transaction in "Invoice History" 
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <!--End Congratulations Section-->

        <!-- Modal footer -->
		
		
		
		
        <div class="row">
            <div class="col-md-12 pad-0">
                    
                <div class="modal-footer justify-center payment-modal-footer upi-footer">
                    <button name="btncancelpayment" id="btncancelpayment" type="button" class="close upi-close" data-dismiss="modal"> Cancel </button>
                <button type="submit"  name="next" id="proceedPaymentButton" onclick="updatePaymentVal(<?php echo $paymentMethodSelected; ?>)" style="display: true;" class="btn btn-primary btn_proceed next proceedPayment m-t-b-10 upi-next-btn">NEXT</button>
                <button type="button" name="next" id="proceedPaymentButtonLoader" style="display: none;" class="btn-wait m-t-b-10"><img src="<?php echo base_url();?>assets/images/ring.gif" alt="Please wait ..." /></button>
                </div>

            </div>
        </div>

    </div>

            

            </section>
            <!-- End payments Section -->
        </div>

    </div>
</div>
</div>

	
	
   
    <!-- End Modal payment -->
    <script>
    $(document).ready(function(){
        $('.categorySelect').select2({
			placeholder: "Please select a class to ride"}
		);
		$('.select2').css("width", "100%");
		$('.categorySelect').on('select2:select', function (e) 
		{
			var content=$('.select2-selection__choice').text();
		//	alert(content);
		/*	content.replace('characterToReplace', '<span class="select2-selection__choice__remove" role="presentation">×</span>'); */
		//	content=content.replace('×','');
		
			//alert(content);
			//content.replace("<span class='select2-selection__choice__remove' role='presentation'>×</span>","");
			//alert(content);
			document.getElementById("selectedclasslist").innerHTML=content;
		//	var classNames = $('.select2-selection__choice').html();
			// alert(innerHtml);
		//	document.getElementById("classeslist").innerHTML=classNames;
		});
    });
    </script>