<div class="content-wrapper">
    <div class="content-header row">
        <div class="content-header-left col-md-6 col-xs-12 mb-2">
            <h3 class="content-header-title mb-0"><?php echo $title; ?></h3>
        </div>
        <!--<div class="content-header-right col-md-6 col-xs-12">
            <div role="group" aria-label="Button group with nested dropdown" class="btn-group float-md-right">
                <div role="group" class="btn-group">
                    <button id="btnGroupDrop1" type="button" data-toggle="dropdown" aria-haspopup="true"
                            aria-expanded="false"
                            class="btn btn-outline-primary dropdown-toggle dropdown-menu-right"><i
                                class="ft-settings icon-left"></i> Settings
                    </button>
                    <div aria-labelledby="btnGroupDrop1" class="dropdown-menu"><a href="card-bootstrap.html"
                                                                                  class="dropdown-item">Bootstrap
                            Cards</a><a href="component-buttons-extended.html" class="dropdown-item">Buttons
                            Extended</a></div>
                </div>
                <a href="calendars-clndr.html" class="btn btn-outline-primary"><i class="ft-mail"></i></a><a
                        href="timeline-center.html" class="btn btn-outline-primary"><i class="ft-pie-chart"></i></a>
            </div>
        </div>-->
    </div>
    <div class="content-body"><!-- Basic form layout section start -->
        <section id="horizontal-form-layouts">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body collapse in">
                            <div class="card-block">
                                
                                <?php if (validation_errors()) { ?>
                                    <div class="alert alert-danger alert-dismissible fade in mb-2" role="alert">
                                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                            <span aria-hidden="true">×</span>
                                        </button>
                                        <?php echo validation_errors(); ?>
                                    </div>
                                <?php } ?>

                                <?php echo form_open('events/add-events', array(
                                    'class' => 'form form-horizontal','enctype'=>'multipart/form-data'
                                )) ?>
             <div class="rightcontain container-fluid">
                <div class="form-group">
                  <label class="col-md-3 control-label" for="textinput">Event Name<span style="color:#F00;"><sup>*</sup></span></label>  
                              <div class="col-md-5">
                                  <input id="events_name" name="events_name" type="text"  class="form-control input-md" required> 
                              </div>
                </div>
                <br clear="all" /><br />
                                    <!-- Textarea -->
                    <div class="form-group">
                      <label class="col-md-3 control-label" for="textarea_objetivo">Organizer</label>
                      <div class="col-md-5">                     
						<select id="events_organizer" name="events_organizer" required class="form-control input-md">
						<option value="">---Select Organizer---</option>
							<?php
							foreach($organiser as $row)
							{
							?>
							<option value="<?php echo $row['users_id'];?>"><?php echo $row['users_name'];?></option>
							<?php
							}
							?>
						  </select>
                      </div>
                    </div>
                <br clear="all" /><br />
                
                <div class="form-group">
                 <label class="col-md-3 control-label" for="textinput">Date</label>  
                     <div class="col-md-5" > 
                      <input type="date" name="events_date" id="events_date" class="form-control input-md" required>
                     </div>
                </div>
                <br clear="all" /><br />
                
                <div class="form-group">
                  <label class="col-md-3 control-label" for="textinput">Venue <span style="color:#F00;"><sup>*</sup></span></label>  
                  <div class="col-md-5">
                  <input id="events_venue" name="events_venue" type="text" placeholder="" class="form-control input-md" required>
                 
                  </div>
                </div>
				<br clear="all" /><br />
                
				<div class="form-group">
                 <label class="col-md-3 control-label" for="textinput">Venue Embeded Code</label>  
                     <div class="col-md-5" > 
                      <textarea  name="events_venue_embeded_code" id="events_venue_embeded_code" class="form-control input-md" ></textarea>
                     </div>
                </div>
                <br clear="all" /><br />
        <!--          <div class="form-group">
                  <label class="col-md-3 control-label" for="date">Time <span style="color:#F00;"><sup>*</sup></span></label>
                  <div class="col-md-2">
                    <select id="hour" name="hour" class="form-control" >
                      <option value="">00</option>
                      <?php
					  for($i=1;$i<=12;$i++)
					  {
						  if($i<10)
						  $temp_val="0".$i;
						  else
						   $temp_val=$i;
					  ?>
                   	<option value="<?php echo $i;?>"><?php echo $temp_val;?></option>
                   	<?php
					  }
					  ?>
                    </select>
                  </div>
                
                  <div class="col-md-2">
                    <select id="minute" name="minute" class="form-control">
                      <option value="0">00</option>
                      <?php
					  for($i=1;$i<=59;$i++)
					  {
						  if($i<10)
						  $temp_val="0".$i;
						  else
						   $temp_val=$i;
					  ?>
                   	<option value="<?php echo $i;?>"><?php echo $temp_val;?></option>
                   	<?php
					  }
					  ?> 
                    </select>
                  </div>
                
                
                  <div class="col-md-1">
                    <select id="time" name="time" class="form-control" style="padding:6px;">
                      <option value="am">AM</option>
                       <option value="pm">PM</option>
                    </select>
                  </div>
                </div>
                  <br clear="all" /><br /> -->

                <div class="form-group">
                 <label class="col-md-3 control-label" for="textinput">Registration Open Status</label>  
            
                      <div class="col-md-1" > 
                       <input type="radio" name="events_reg_open_status" id="events_reg_open_status_y" class="form-control input-md" value="1" checked="checked">
                     </div>
                     <label class="col-md-1 control-label" for="textinput">Yes</label>
                      <div class="col-md-1" > 
                     <input type="radio" name="events_reg_open_status" id="events_reg_open_status_n" class="form-control input-md" value="0">
                     </div>
                     <label class="col-md-1 control-label" for="textinput">No</label>                      
                </div>
                <br clear="all" /><br />
                <div class="form-group">
                 <label class="col-md-3 control-label" for="textinput">Enable Cash On Event (COE)?</label>  
                     <div class="col-md-1" > 
                       <input type="radio" name="events_is_coe" id="events_is_coe" class="form-control input-md" value="1">
                     </div>
                     <label class="col-md-1 control-label" for="textinput">Yes</label>
                      <div class="col-md-1" > 
                     <input type="radio" name="events_is_coe" id="events_is_coe" class="form-control input-md" value="0"checked="checked" >
                     </div>
                     <label class="col-md-1 control-label" for="textinput">No</label>
                </div>
                <br clear="all" /><br />
                <div class="form-group">
                 <label class="col-md-3 control-label" for="textinput">Enable Online Payment?</label>  
                     <div class="col-md-1" > 
                       <input type="radio" name="events_is_online_pay" id="events_is_online_pay" class="form-control input-md" value="1" checked="checked">
                     </div>
                     <label class="col-md-1 control-label" for="textinput">Yes</label>
                      <div class="col-md-1" > 
                     <input type="radio" name="events_is_online_pay" id="events_is_online_pay" class="form-control input-md" value="0" >
                     </div>
                     <label class="col-md-1 control-label" for="textinput">No</label>
                </div>
                <br clear="all" /><br />
				
				 <div class="form-group">
                 <label class="col-md-3 control-label" for="textinput">Enable Bank Transaction?</label>  
                     <div class="col-md-1" > 
                       <input type="radio" name="is_bank_transaction" id="is_bank_transaction" class="form-control input-md" value="1" checked="checked">
                     </div>
                     <label class="col-md-1 control-label" for="textinput">Yes</label>
                      <div class="col-md-1" > 
                     <input type="radio" name="is_bank_transaction" id="is_bank_transaction" class="form-control input-md" value="0" >
                     </div>
                     <label class="col-md-1 control-label" for="textinput">No</label>
                </div>
                <br clear="all" /><br />

                <div class="form-group">
                 <label class="col-md-3 control-label" for="textinput">Enable FMSCI License?</label>  
                     <div class="col-md-1" > 
                       <input type="radio" name="events_is_fmsci" id="events_is_fmsci" class="form-control input-md" value="1" >
                     </div>
                     <label class="col-md-1 control-label" for="textinput">Yes</label>
                      <div class="col-md-1" > 
                     <input type="radio" name="events_is_fmsci" id="events_is_fmsci" class="form-control input-md" value="0" checked="checked">
                     </div>
                     <label class="col-md-1 control-label" for="textinput">No</label>
                </div>
                <br clear="all" /><br />
                
			          <div class="form-group">
                 <label class="col-md-3 control-label" for="textinput">Enable PAN Card?</label>  
                     <div class="col-md-1" > 
                       <input type="radio" name="events_is_pan_card" id="events_is_pan_card" class="form-control input-md" value="1" >
                     </div>
                     <label class="col-md-1 control-label" for="textinput">Yes</label>
                      <div class="col-md-1" > 
                     <input type="radio" name="events_is_pan_card" id="events_is_pan_card" class="form-control input-md" value="0" checked="checked">
                     </div>
                     <label class="col-md-1 control-label" for="textinput">No</label>
                </div>
                <br clear="all" /><br />
                <div class="form-group">
                 <label class="col-md-3 control-label" for="textinput">Enable PA Policy?</label>  
                     <div class="col-md-1" > 
                       <input type="radio" name="events_is_pa_policy" id="events_is_pa_policy" class="form-control input-md" value="1" >
                     </div>
                     <label class="col-md-1 control-label" for="textinput">Yes</label>
                      <div class="col-md-1" > 
                     <input type="radio" name="events_is_pa_policy" id="events_is_pa_policy" class="form-control input-md" value="0" checked="checked">
                     </div>
                     <label class="col-md-1 control-label" for="textinput">No</label>
                </div>
                <br clear="all" /><br />
				 
				<div class="form-group">
                 <label class="col-md-3 control-label" for="textinput">Enable Team Name?</label>  
                     <div class="col-md-1" > 
                       <input type="radio" name="events_team_name" id="events_team_name" class="form-control input-md special-package" value="1" >
                     </div>
                     <label class="col-md-1 control-label" for="textinput">Yes</label>
                      <div class="col-md-1" > 
                     <input type="radio" name="events_team_name" id="events_team_name" class="form-control input-md special-package" value="0" checked="checked">
                     </div>
                     <label class="col-md-1 control-label" for="textinput">No</label>
                </div>
				<br clear="all" /><br />
				
               
			    <div class="form-group">
                 <label class="col-md-3 control-label" for="textinput">Enable Name of Tuner?</label>  
                     <div class="col-md-1" > 
                       <input type="radio" name="events_is_name_of_tuner" id="events_is_name_of_tuner" class="form-control input-md special-package" value="1" >
                     </div>
                     <label class="col-md-1 control-label" for="textinput">Yes</label>
                      <div class="col-md-1" > 
                     <input type="radio" name="events_is_name_of_tuner" id="events_is_name_of_tuner" class="form-control input-md special-package" value="0" checked="checked">
                     </div>
                     <label class="col-md-1 control-label" for="textinput">No</label>
                </div>
				<br clear="all" /><br />
				
				
			   
            <input type="hidden" name="events_FMSCI_fee" id="events_FMSCI_fee" class="form-control input-md" value="0">
       <!--       <div id="special-package-group" class="d-none">     
                <div class="form-group">
                 <label class="col-md-3 control-label" for="textinput">Special Package Name</label>  
                     <div class="col-md-5" > 
                      <input type="text" name="events_special_package_title" id="events_special_package_title" class="form-control input-md" value="">
                     </div>
                </div>
                <br clear="all" /><br />
                <div class="form-group">
                 <label class="col-md-3 control-label" for="textinput">Special Package Amount</label>  
                     <div class="col-md-5" > 
                      <input type="text" name="events_special_package_amount" id="events_special_package_amount" class="form-control input-md" value="">
                     </div>
                </div>
                <br clear="all" /><br />
          </div>-->
              <!--
                <div class="form-group">
                 <label class="col-md-3 control-label" for="textinput">FMSCI Club Spot license Fee</label>  
                     <div class="col-md-5" > 
                      <input type="text" name="events_FMSCI_fee" id="events_FMSCI_fee" class="form-control input-md" value="0">
                     </div>
                </div>
                <br clear="all" /><br />
            -->
            <div class="form-group">
                 <label class="col-md-3 control-label" for="textinput">Featured Image</label>  
                     <div class="col-md-5" > 
                      <input type="text" name="events_featured_image" id="events_featured_image" class="form-control input-md" value="">
                     </div>
                </div>
                <br clear="all" /><br />
                <div class="form-group">
                 <label class="col-md-3 control-label" for="textinput">Sponsors image</label>  
                     <div class="col-md-5" > 
                      <input type="text" name="events_sponsor_image" id="events_sponsor_image" class="form-control input-md" value="">
                     </div>
                </div>
                <br clear="all" /><br />
                <div class="form-group">
                 <label class="col-md-3 control-label" for="textinput">Description</label>  
                     <div class="col-md-5" > 
                      <textarea  name="events_description" id="events_description" class="form-control input-md" ></textarea>
                     </div>
                </div>
                <br clear="all" /><br />
				
				<div class="form-group">
                 <label class="col-md-3 control-label" for="textinput">Confirm Message</label>  
                     <div class="col-md-5" > 
                      <textarea name="events_confirm_message" id="events_confirm_message" class="form-control input-md" ></textarea>
                     </div>
                </div>
                <br clear="all" /><br />
				
				
                <div class="form-group">
                 <label class="col-md-3 control-label" for="textinput">Organizer Logo</label>  
                     <div class="col-md-5" > 
                      <input type="text" name="events_organizer_logo" id="events_organizer_logo" class="form-control input-md" value="">
                     </div>
                </div>
                <br clear="all" /><br />



				<div class="form-group">
                 <label class="col-md-3 control-label" for="textinput">Competition No From</label>  
                     <div class="col-md-5" > 
                      <input type="text" name="events_competitionno_from" id="events_competitionno_from" class="form-control input-md" value="" required>
                     </div>
                </div>
                <br clear="all" /><br />
				
				
			   <div class="form-group">
                 <label class="col-md-3 control-label" for="textinput">Competition No UpTo</label>  
                     <div class="col-md-5" > 
                      <input type="text" name="events_competitionno_upto" id="events_competitionno_upto" class="form-control input-md" required>
                     </div>
                </div>
                <br clear="all" /><br />


                <div id="aditional_category">
                 <div class="form-group">
                     <label class="col-md-3 control-label" for="textinput"><b>Categories  </b></label>  
                   </div>
                   <br clear="all" /><br />
                   <div class="form-group">
                     <label class="col-md-1 control-label" for="textinput">Name  </label>  
                      <div class="col-md-3">
                       <input id="events_cat1" name="events_cat1" type="text"  class="form-control input-small" required> 
                      </div>
                     <label class="col-md-1 control-label" for="textinput">Fee  </label>  
                      <div class="col-md-3">
                       <input id="events_cat_rate1" name="events_cat_rate1" type="text"  class="form-control input-small" required> 
                      </div>
                      <label class="col-md-1 control-label" for="textinput">Category Type  </label>  
                      <div class="col-md-3">
                       <select id="events_cat_type1" name="events_cat_type1"  class="form-control input-small" required> 
                        <?php
                         foreach($bike_category_types as $bike_category_type)
                         {
                          echo '<option value="'.$bike_category_type["bike_vehicle_category_type_id"].'">'.$bike_category_type["bike_vehicle_category_type_name"].'</option>';

                         }
                        ?>
                       </select>
                      </div>  
                      <br clear="all" /><br />                       
                      <label class="col-md-1 control-label" for="textinput">Prices Money  </label>  
                      <div class="col-md-3">
                       <textarea id="events_cat_prices1" name="events_cat_prices1"  class="form-control input-small" required> 

                       </textarea>
                      </div>                      
                      
                      

                 	</div>
                    <br clear="all" /><br />
                    <div class="form-group">
                      <input type="button" class="btmargin btn btn-primary" id="addcat" name="addcat" value="Add More" style="padding:5px 10px;">
                     </div>
                 <br clear="all" /><br />
                </div>  
                   <input type="hidden" value="1" id="cat_count" name="cat_count" />
                   
                   

                 

  <br clear="all" /><br />
				
    </div>

                                    <div class="form-actions right">
                                        <button type="button" class="btn btn-warning mr-1">
                                            <i class="ft-x"></i> Cancel
                                        </button>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fa fa-check-square-o"></i> Save
                                        </button>
                                    </div>
                               <?php echo form_close(); ?>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- // Basic form layout section end -->
    </div>
</div>
<script>

var rad = document.querySelectorAll('input[type=radio][name="events_is_special_package"]');
rad.forEach(radio => radio.addEventListener('change', () => {
if(radio.value == 1){
        document.querySelector("#special-package-group").classList.remove("d-none");
      }
      else
      document.querySelector("#special-package-group").classList.add("d-none");

    }));

</script>