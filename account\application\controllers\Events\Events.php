<?php
date_default_timezone_set('Asia/Kolkata');
use \PhpOffice\PhpSpreadsheet\Spreadsheet;
use \PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use \PhpOffice\PhpSpreadsheet\Style\Border;
// use \PhpOffice\PhpSpreadsheet\Writer\Pdf\Mpdf;
class Events extends TP_Controller
{
    function __construct() 
    {
        parent::__construct(); 
        $this->load->model('events_model');
		$this->load->model('participants_model');
		$this->load->model('adds_model');
    }    

    const version = '1.1';

    protected $curl;

    /* Sandbox details */

 /* protected $endpoint = 'https://test.instamojo.com/api/1.1/payment-requests/';
    protected $api_key = 'test_efbda20f5cab6724da6b9e68b65';//null;
    protected $auth_token = 'test_8d36e7cd315d8a1aa8181cd6798';//null;*/

    /*
    function __construct($api_key='test_efbda20f5cab6724da6b9e68b65', $auth_token='test_8d36e7cd315d8a1aa8181cd6798', $endpoint='https://test.instamojo.com/api/1.1/')

    */
    /* Sandbox details */
    /* Live details */

    protected $endpoint = 'https://www.instamojo.com/api/1.1/';
    protected $api_key = '10d2d5816911b14c3b1a9d66cc57ad47';  //null;
    protected $auth_token = '15b477b0ddf8483978526d01236153e5';   //null;


    /* Live details */



    public function index()
    {	
		if($this->session->userdata('user_type') == 0)
		{
		   $data['loginType'] = "Admin";
		}
		if($this->session->userdata('user_type') == 1)
		{
		   $data['loginType'] = "Organiser";
		}
			
        if($this->session->userdata('user_id')==NULL)
        {
            redirect("login");
        }
        else if($this->session->userdata('user_type') == 2)
        {
		    $data['events'] = $this->events_model->getEvents();
			
			$data['events_dash_board_details'] = $this->events_model->getDashBoardDeatils();
            $data['categories'] = array();//$this->events_model->getCategoryOfEvent($id);
            $data['transactions']= $this->events_model->getUPIPaymentTransactions();
			$cartransactions = $this->events_model->getCarUPIPaymentTransactions();
			$i=-1;
			foreach($cartransactions as $cartransaction)
			{
			   $i++;
			//   $participantsId=$cartransaction['events_details_participants_id'];
			 //  $participantId=$cartransaction['participants_id'];
			   $participantId=$cartransaction['participants_id'];
			   $eventId=$cartransaction['payment_event_id'];
			   if($participantId != "")
			   {
					   $cartransactions[$i]['documents_name']=$this->events_model->getParticipantDocDetails($participantId);
					   $categoryIdsDetails=$this->events_model->getCarParticipantCategoryDetails($participantId, $eventId);
				//	   $categoryIds=explode(",",$cartransaction['Category Id']);
					   $categoryIds=explode(",",$categoryIdsDetails);
					   $classestoride="";
					   foreach($categoryIds as $categoryId)
					   {
					      if($categoryId != "")
						  {
							 if($classestoride != "")
							 {
								 $classestoride=$classestoride . ",";
							 }
							 $classestoride=$classestoride . $this->events_model->getCarCategoryDetails($categoryId);
						  }
					   }
					   $cartransactions[$i]['Classes_to_Ride']=$classestoride;
					   $paymentId=$cartransaction['payment_id'];
					   $cartransactions[$i]['upi_details_transaction_id']=$this->events_model->getCarUpiDet($paymentId);
					   $participantId=$cartransaction['participants_id'];
					   $eventsId=$cartransaction['events_id'];
					   if(($participantId != "") && ($eventsId != ""))
					   {
					       $cartransactions[$i]['Competetion Number']=$this->events_model->getCompletionNo($participantId,$eventsId);
					   }
			  }
			}
			$data['cartransactions']= $cartransactions;
			
			$bikerallytransactions = $this->events_model->getBikeRallyUPIPaymentTransactions();
			$j=-1;
			foreach($bikerallytransactions as $bikerallytransaction)
			{
			   $j++;
			//   $participantsId=$bikerallytransaction['events_details_participants_id'];
			   $participantId=$bikerallytransaction['participants_id'];
			   $eventId=$bikerallytransaction['payment_event_id'];
			   if($participantId != "")
			   {
					   $bikerallytransactions[$j]['documents_name']=$this->events_model->getParticipantDocDetails($participantId);
					   $categoryIdsDetails=$this->events_model->getBikeRallyParticipantCategoryDetails( $participantId, $eventId);
					  // $categoryIds=explode(",",$bikerallytransaction['Category Id']);
					  $categoryIds=explode(",",$categoryIdsDetails);
					   $classestoride="";
					   foreach($categoryIds as $categoryId)
					   {
					      if($categoryId != "")
						  {
							 if($classestoride != "")
							 {
								 $classestoride=$classestoride . ",";
							 }
							 $classestoride=$classestoride . $this->events_model->getBikeRallyCategoryDetails($categoryId);
						  }
					   }
					   $bikerallytransactions[$j]['Classes_to_Ride']=$classestoride;
					   $paymentId=$bikerallytransaction['payment_id'];
					   $bikerallytransactions[$j]['upi_details_transaction_id']=$this->events_model->getBikeRallyUpiDet($paymentId);
					   $participantId=$bikerallytransaction['participants_id'];
					   $eventsId=$bikerallytransaction['events_id'];
					   if(($participantId != "") && ($eventsId != ""))
					   {
					       $bikerallytransactions[$j]['Competetion Number']=$this->events_model->getBikeRallyCompletionNo($participantId,$eventsId);
					   }
			  }
			}
			
			$data['bikerallytransactions']= $bikerallytransactions;
			
			
            $this->template->write('title', 'Dashboard', TRUE);
            $this->template->add_js('assets/js/upcoming-list.js');
			 $this->template->add_js('assets/vendors/js/jquery.PrintArea.js');
            $this->template->write_view('content', 'templates/pages/Events/dashboard', $data, TRUE);
            $this->template->render();
        }
        else     //  admin or organiser login
		{
		//	echo "in admin/organiser";
            $data['events'] = $this->events_model->getEvents();
            $data['events_dash_board_details'] = $this->events_model->getDashBoardDeatils();
            $data['categories'] = array();//$this->events_model->getCategoryOfEvent($id);
            $transactions = $this->events_model->getUPIPaymentTransactions();
		//	print_r($transactions);
		//
			$i=-1;

			// Optimized: Get all participant IDs and event IDs for bulk operations
			$participant_ids = array();
			$event_ids = array();
			$payment_ids = array();
			$participant_event_pairs = array();
			if($transactions)
			{
				foreach($transactions as $transaction)
				{
					if($transaction['participants_id'] != "")
					{
						$participant_ids[] = $transaction['participants_id'];
						$event_ids[] = $transaction['events_id'];
						$payment_ids[] = $transaction['payment_id'];
						$participant_event_pairs[] = array(
							'participant_id' => $transaction['participants_id'],
							'event_id' => $transaction['events_id']
						);
					}
				}
			}

			// Bulk fetch participant documents
			$participant_docs = $this->events_model->getParticipantDocDetailsBulk(array_unique($participant_ids));

			// Bulk fetch data for regular bike transactions
			$participant_categories = $this->events_model->getParticipantCategoryDetailsBulk($participant_event_pairs);
			$upi_details = $this->events_model->getUpiDetailsBulk(array_unique($payment_ids));
			$competition_numbers = $this->events_model->getCompletionNumbersBulk($participant_event_pairs);

			// Get all unique category IDs for bulk category details fetch
			$all_category_ids = array();
			foreach($participant_categories as $category_string)
			{
				if($category_string)
				{
					$category_ids = explode(",", $category_string);
					$all_category_ids = array_merge($all_category_ids, $category_ids);
				}
			}
			$category_details = $this->events_model->getCategoryDetailsBulk(array_unique(array_filter($all_category_ids)));

			// Bulk fetch event category details for all events
			$event_categories = array();
			foreach(array_unique($event_ids) as $event_id)
			{
				$event_categories[$event_id] = $this->events_model->getEventCategoryDetailsOptimized($event_id);
			}

			if($transactions)
			{
					foreach($transactions as $transaction)
					{
							//	echo "event id = " . $transaction['events_id'] . "<br>";
								$transEventTotEntries=0;
								$transEventTotConfirmEntries=0;
								$transEventTotPendingEntries=0;
								$transEventTotPaidAmount=0;
								$transEventTotPendingAmount=0;
								$i++;


								$participantId=$transaction['participants_id'];
								$eventId=$transaction['payment_event_id'];
								if($participantId != "")
								{
												// Use bulk fetched data instead of individual queries
												$transactions[$i]['documents_name'] = isset($participant_docs[$participantId]) ? $participant_docs[$participantId] : '';

												// Build Classes_to_Ride from bulk data
												$participant_event_key = $participantId . '_' . $eventId;
												$categoryIdsDetails = isset($participant_categories[$participant_event_key]) ? $participant_categories[$participant_event_key] : '';
												$categoryIds=explode(",",$categoryIdsDetails);
												$classestoride="";
												foreach($categoryIds as $categoryId)
												{
												   if($categoryId != "" && isset($category_details[$categoryId]))
													  {
														 if($classestoride != "")
														 {
															 $classestoride=$classestoride . ",";
														 }
														 $classestoride=$classestoride . $category_details[$categoryId];
													  }
												}
												$transactions[$i]['Classes_to_Ride']=$classestoride;

												// Add UPI details from bulk data
												$paymentId=$transaction['payment_id'];
												$transactions[$i]['upi_details_transaction_id'] = isset($upi_details[$paymentId]) ? $upi_details[$paymentId] : '';

												// Add Competition Number from bulk data
												$participantId=$transaction['participants_id'];
												$eventsId=$transaction['events_id'];
												$participant_event_key = $participantId . '_' . $eventsId;
												$transactions[$i]['Competetion Number'] = isset($competition_numbers[$participant_event_key]) ? $competition_numbers[$participant_event_key] : '';

												// Use optimized category details
												$categoryDet = isset($event_categories[$transaction['events_id']]) ? $event_categories[$transaction['events_id']] : array();

												// Calculate totals from optimized data
												foreach($categoryDet as $catDet)
												{
													$transEventTotEntries += $catDet['eventTotEntries'];
													$transEventTotConfirmEntries += $catDet['eventConfirmEntries'];
													$transEventTotPendingEntries += $catDet['eventPendingEntries'];
													$transEventTotPaidAmount += $catDet['eventPaidAmount'];
													$transEventTotPendingAmount += $catDet['eventPendingAmount'];
												}

											$transactions[$i]['categories']=$categoryDet;
								  }

					}
			}
			$data['transactions']=$transactions;
		
		
		
		
			
			$cartransactions = $this->events_model->getCarUPIPaymentTransactions();
			$i=-1;

			// Optimized: Prepare bulk data for car transactions
			$car_participant_ids = array();
			$car_event_ids = array();
			$car_payment_ids = array();
			$car_participant_event_pairs = array();

			foreach($cartransactions as $cartransaction)
			{
				if($cartransaction['participants_id'] != "")
				{
					$car_participant_ids[] = $cartransaction['participants_id'];
					$car_event_ids[] = $cartransaction['events_id'];
					$car_payment_ids[] = $cartransaction['payment_id'];
					$car_participant_event_pairs[] = array(
						'participant_id' => $cartransaction['participants_id'],
						'event_id' => $cartransaction['events_id']
					);
				}
			}

			// Bulk fetch data for car transactions
			$car_participant_docs = $this->events_model->getParticipantDocDetailsBulk(array_unique($car_participant_ids));
			$car_participant_categories = $this->events_model->getCarParticipantCategoryDetailsBulk($car_participant_event_pairs);
			$car_upi_details = $this->events_model->getCarUpiDetailsBulk(array_unique($car_payment_ids));
			$car_competition_numbers = $this->events_model->getCarCompletionNumbersBulk($car_participant_event_pairs);

			// Get all unique category IDs for bulk category details fetch
			$all_category_ids = array();
			foreach($car_participant_categories as $category_string)
			{
				if($category_string)
				{
					$category_ids = explode(",", $category_string);
					$all_category_ids = array_merge($all_category_ids, $category_ids);
				}
			}
			$car_category_details = $this->events_model->getCarCategoryDetailsBulk(array_unique(array_filter($all_category_ids)));

			// Bulk fetch car event category details for all events
			$car_event_categories = array();
			foreach(array_unique($car_event_ids) as $event_id)
			{
				$car_event_categories[$event_id] = $this->events_model->getCarEventCategoryDetailsOptimized($event_id);
			}

			foreach($cartransactions as $cartransaction)
			{
			   $cartransEventTotEntries=0;
			   $cartransEventTotConfirmEntries=0;
			   $cartransEventTotPendingEntries=0;
			   $cartransEventTotPaidAmount=0;
			   $cartransEventTotPendingAmount=0;
			   $i++;
			   $participantId=$cartransaction['participants_id'];
			   $eventId=$cartransaction['payment_event_id'];
			   if($participantId != "")
			   {
					   // Use bulk fetched data
					   $cartransactions[$i]['documents_name'] = isset($car_participant_docs[$participantId]) ? $car_participant_docs[$participantId] : '';

					   $participant_event_key = $participantId . '_' . $eventId;
					   $categoryIdsDetails = isset($car_participant_categories[$participant_event_key]) ? $car_participant_categories[$participant_event_key] : '';
					   $categoryIds=explode(",",$categoryIdsDetails);
					   $classestoride="";
					   foreach($categoryIds as $categoryId)
					   {
					      if($categoryId != "" && isset($car_category_details[$categoryId]))
						  {
							 if($classestoride != "")
							 {
								 $classestoride=$classestoride . ",";
							 }
							 $classestoride=$classestoride . $car_category_details[$categoryId];
						  }
					   }
					   $cartransactions[$i]['Classes_to_Ride']=$classestoride;

					   $paymentId=$cartransaction['payment_id'];
					   $cartransactions[$i]['upi_details_transaction_id'] = isset($car_upi_details[$paymentId]) ? $car_upi_details[$paymentId] : '';

					   $participantId=$cartransaction['participants_id'];
					   $eventsId=$cartransaction['events_id'];
					   $participant_event_key = $participantId . '_' . $eventsId;
					   $cartransactions[$i]['Competetion Number'] = isset($car_competition_numbers[$participant_event_key]) ? $car_competition_numbers[$participant_event_key] : '';

					   // Use optimized category details
					   $categoryDet = isset($car_event_categories[$cartransaction['events_id']]) ? $car_event_categories[$cartransaction['events_id']] : array();

					   // Calculate totals from optimized data
					   foreach($categoryDet as $catDet)
					   {
						   $cartransEventTotEntries += $catDet['eventTotEntries'];
						   $cartransEventTotConfirmEntries += $catDet['eventConfirmEntries'];
						   $cartransEventTotPendingEntries += $catDet['eventPendingEntries'];
						   $cartransEventTotPaidAmount += $catDet['eventPaidAmount'];
						   $cartransEventTotPendingAmount += $catDet['eventPendingAmount'];
					   }

					    $cartransactions[$i]['categories']=$categoryDet;
						$cartransactions[$i]['eventTotEntries']=$cartransEventTotEntries;
						$cartransactions[$i]['eventConfirmEntries']=$cartransEventTotConfirmEntries;
						$cartransactions[$i]['eventPendingEntries']=$cartransEventTotPendingEntries;
						$cartransactions[$i]['eventPaidAmount']=$cartransEventTotPaidAmount;
						$cartransactions[$i]['eventPendingAmount']=$cartransEventTotPendingAmount;
			  }
			}
			$data['cartransactions']= $cartransactions;
			
			
			
			
			
			
			$bikerallytransactions = $this->events_model->getBikeRallyUPIPaymentTransactions();
			$j=-1;

			// Optimized: Prepare bulk data for bike rally transactions
			$bikerally_participant_ids = array();
			$bikerally_event_ids = array();
			$bikerally_payment_ids = array();
			$bikerally_participant_event_pairs = array();

			foreach($bikerallytransactions as $bikerallytransaction)
			{
				if($bikerallytransaction['participants_id'] != "")
				{
					$bikerally_participant_ids[] = $bikerallytransaction['participants_id'];
					$bikerally_event_ids[] = $bikerallytransaction['events_id'];
					$bikerally_payment_ids[] = $bikerallytransaction['payment_id'];
					$bikerally_participant_event_pairs[] = array(
						'participant_id' => $bikerallytransaction['participants_id'],
						'event_id' => $bikerallytransaction['events_id']
					);
				}
			}

			// Bulk fetch data for bike rally transactions
			$bikerally_participant_docs = $this->events_model->getParticipantDocDetailsBulk(array_unique($bikerally_participant_ids));
			$bikerally_participant_categories = $this->events_model->getBikeRallyParticipantCategoryDetailsBulk($bikerally_participant_event_pairs);
			$bikerally_upi_details = $this->events_model->getBikeRallyUpiDetailsBulk(array_unique($bikerally_payment_ids));
			$bikerally_competition_numbers = $this->events_model->getBikeRallyCompletionNumbersBulk($bikerally_participant_event_pairs);

			// Get all unique category IDs for bulk category details fetch
			$all_bikerally_category_ids = array();
			foreach($bikerally_participant_categories as $category_string)
			{
				if($category_string)
				{
					$category_ids = explode(",", $category_string);
					$all_bikerally_category_ids = array_merge($all_bikerally_category_ids, $category_ids);
				}
			}
			$bikerally_category_details = $this->events_model->getBikeRallyCategoryDetailsBulk(array_unique(array_filter($all_bikerally_category_ids)));

			// Bulk fetch bike rally event category details for all events
			$bikerally_event_categories = array();
			foreach(array_unique($bikerally_event_ids) as $event_id)
			{
				$bikerally_event_categories[$event_id] = $this->events_model->getBikeRallyEventCategoryDetailsOptimized($event_id);
			}

			foreach($bikerallytransactions as $bikerallytransaction)
			{
			   $bikerallytransEventTotEntries=0;
			   $bikerallytransEventTotConfirmEntries=0;
			   $bikerallytransEventTotPendingEntries=0;
			   $bikerallytransEventTotPaidAmount=0;
			   $bikerallytransEventTotPendingAmount=0;
			   $j++;
			   $participantId=$bikerallytransaction['participants_id'];
			   $eventId=$bikerallytransaction['payment_event_id'];
			   			   if($participantId != "")
						   {
										   // Use bulk fetched data
										   $bikerallytransactions[$j]['documents_name'] = isset($bikerally_participant_docs[$participantId]) ? $bikerally_participant_docs[$participantId] : '';

										   $participant_event_key = $participantId . '_' . $eventId;
										   $categoryIdsDetails = isset($bikerally_participant_categories[$participant_event_key]) ? $bikerally_participant_categories[$participant_event_key] : '';
										   $categoryIds=explode(",",$categoryIdsDetails);
										   $classestoride="";
										   foreach($categoryIds as $categoryId)
										   {
											  if($categoryId != "" && isset($bikerally_category_details[$categoryId]))
											  {
												 if($classestoride != "")
												 {
													 $classestoride=$classestoride . ",";
												 }
												 $classestoride=$classestoride . $bikerally_category_details[$categoryId];
											  }
										   }

										   $bikerallytransactions[$j]['Classes_to_Ride']=$classestoride;

										   $paymentId=$bikerallytransaction['payment_id'];
										   $bikerallytransactions[$j]['upi_details_transaction_id'] = isset($bikerally_upi_details[$paymentId]) ? $bikerally_upi_details[$paymentId] : '';

										   $participantId=$bikerallytransaction['participants_id'];
										   $eventsId=$bikerallytransaction['events_id'];
										   $participant_event_key = $participantId . '_' . $eventsId;
										   $bikerallytransactions[$j]['Competetion Number'] = isset($bikerally_competition_numbers[$participant_event_key]) ? $bikerally_competition_numbers[$participant_event_key] : '';

										   // Use optimized category details
										   $categoryDet = isset($bikerally_event_categories[$bikerallytransaction['events_id']]) ? $bikerally_event_categories[$bikerallytransaction['events_id']] : array();

										   // Calculate totals from optimized data
										   foreach($categoryDet as $catDet)
										   {
											   $bikerallytransEventTotEntries += $catDet['eventTotEntries'];
											   $bikerallytransEventTotConfirmEntries += $catDet['eventConfirmEntries'];
											   $bikerallytransEventTotPendingEntries += $catDet['eventPendingEntries'];
											   $bikerallytransEventTotPaidAmount += $catDet['eventPaidAmount'];
											   $bikerallytransEventTotPendingAmount += $catDet['eventPendingAmount'];
										   }

											$bikerallytransactions[$j]['categories']=$categoryDet;
						  }
			}
			$data['bikerallytransactions']= $bikerallytransactions;
			
			
			
			
			$trainingprogramtransactions = $this->events_model->getTrainingProgramUPIPaymentTransactions();
			$i=-1;

			// Optimized: Prepare bulk data for training program transactions
			$training_participant_ids = array();
			$training_event_ids = array();
			$training_payment_ids = array();
			$training_participant_event_pairs = array();

			foreach($trainingprogramtransactions as $trainingprogramtransaction)
			{
				if($trainingprogramtransaction['participants_id'] != "")
				{
					$training_participant_ids[] = $trainingprogramtransaction['participants_id'];
					$training_event_ids[] = $trainingprogramtransaction['events_id'];
					$training_payment_ids[] = $trainingprogramtransaction['payment_id'];
					$training_participant_event_pairs[] = array(
						'participant_id' => $trainingprogramtransaction['participants_id'],
						'event_id' => $trainingprogramtransaction['events_id']
					);
				}
			}

			// Bulk fetch data for training program transactions
			$training_participant_docs = $this->events_model->getParticipantDocDetailsBulk(array_unique($training_participant_ids));
			$training_participant_categories = $this->events_model->getTrainingProgramParticipantCategoryDetailsBulk($training_participant_event_pairs);
			$training_upi_details = $this->events_model->getTrainingProgramUpiDetailsBulk(array_unique($training_payment_ids));
			$training_competition_numbers = $this->events_model->getTrainingProgramCompletionNumbersBulk($training_participant_event_pairs);

			// Get all unique category IDs for bulk category details fetch
			$all_training_category_ids = array();
			foreach($training_participant_categories as $category_string)
			{
				if($category_string)
				{
					$category_ids = explode(",", $category_string);
					$all_training_category_ids = array_merge($all_training_category_ids, $category_ids);
				}
			}
			$training_category_details = $this->events_model->getTrainingProgramCategoryDetailsBulk(array_unique(array_filter($all_training_category_ids)));

			// Bulk fetch training program event category details for all events
			$training_event_categories = array();
			foreach(array_unique($training_event_ids) as $event_id)
			{
				$training_event_categories[$event_id] = $this->events_model->getTrainingProgramEventCategoryDetailsOptimized($event_id);
			}

			foreach($trainingprogramtransactions as $trainingprogramtransaction)
			{
			   $trainingprogramtransEventTotEntries=0;
			   $trainingprogramtransEventTotConfirmEntries=0;
			   $trainingprogramtransEventTotPendingEntries=0;
			   $trainingprogramtransEventTotPaidAmount=0;
			   $trainingprogramtransEventTotPendingAmount=0;
			   $i++;
			   $participantId=$trainingprogramtransaction['participants_id'];
			   $eventId=$trainingprogramtransaction['payment_event_id'];
			   if($participantId != "")
			   {
					   // Use bulk fetched data
					   $trainingprogramtransactions[$i]['documents_name'] = isset($training_participant_docs[$participantId]) ? $training_participant_docs[$participantId] : '';

					   $participant_event_key = $participantId . '_' . $eventId;
					   $categoryIdsDetails = isset($training_participant_categories[$participant_event_key]) ? $training_participant_categories[$participant_event_key] : '';
					   $categoryIds=explode(",",$categoryIdsDetails);
					   $classestoride="";
					   foreach($categoryIds as $categoryId)
					   {
					      if($categoryId != "" && isset($training_category_details[$categoryId]))
						  {
							 if($classestoride != "")
							 {
								 $classestoride=$classestoride . ",";
							 }
							 $classestoride=$classestoride . $training_category_details[$categoryId];
						  }
					   }
					   $trainingprogramtransactions[$i]['Classes_to_Ride']=$classestoride;

					   $paymentId=$trainingprogramtransaction['payment_id'];
					   $trainingprogramtransactions[$i]['upi_details_transaction_id'] = isset($training_upi_details[$paymentId]) ? $training_upi_details[$paymentId] : '';

					   $participantId=$trainingprogramtransaction['participants_id'];
					   $eventsId=$trainingprogramtransaction['events_id'];
					   $participant_event_key = $participantId . '_' . $eventsId;
					   $trainingprogramtransactions[$i]['Competetion Number'] = isset($training_competition_numbers[$participant_event_key]) ? $training_competition_numbers[$participant_event_key] : '';
					   // Use optimized category details
					   $categoryDet = isset($training_event_categories[$trainingprogramtransaction['events_id']]) ? $training_event_categories[$trainingprogramtransaction['events_id']] : array();

					   // Calculate totals from optimized data
					   foreach($categoryDet as $catDet)
					   {
						   $trainingprogramtransEventTotEntries += $catDet['eventTotEntries'];
						   $trainingprogramtransEventTotConfirmEntries += $catDet['eventConfirmEntries'];
						   $trainingprogramtransEventTotPendingEntries += $catDet['eventPendingEntries'];
						   $trainingprogramtransEventTotPaidAmount += $catDet['eventPaidAmount'];
						   $trainingprogramtransEventTotPendingAmount += $catDet['eventPendingAmount'];
					   }

					  $trainingprogramtransactions[$i]['categories']=$categoryDet;
						/* $trainingprogramtransactions[$i]['eventTotEntries']=$trainingprogramtransEventTotEntries; // $eventTotEntries;
						$trainingprogramtransactions[$i]['eventConfirmEntries']=$trainingprogramtransEventTotConfirmEntries; // $eventConfirmEntries;
						$trainingprogramtransactions[$i]['eventPendingEntries']=$trainingprogramtransEventTotPendingEntries; // $eventPendingEntries;
						$trainingprogramtransactions[$i]['eventPaidAmount']=$trainingprogramtransEventTotPaidAmount; // $eventPaidAmount;
						$trainingprogramtransactions[$i]['eventPendingAmount']=$trainingprogramtransEventTotPendingAmount; // $eventPendingAmount;*/
					//	$trainingprogramtransactions[$i]['confirmTrainingProgramTransactions']=$this->events_model->getTrainingProgramUPIPaymentTransactionsHistoryByEvent($trainingprogramtransaction['events_id']); // For speed testinc commented - Feb 17th 2024
			  }
			}
			$data['trainingprogramtransactions']= $trainingprogramtransactions;
			// echo "<pre>";
			/*print_r($data['trainingprogramtransactions']);
			exit;*/
			
		
			
		//	$this->template->add_css('assets/css/bootstrap.min.css');  // For speed testinc commented - Feb 17th 2024
		//	$this->template->add_css('assets/css/style.css');	// For speed testinc commented - Feb 17th 2024
		//	$this->template->add_css('assets/css/responsive.css');  // For speed testinc commented - Feb 17th 2024
			
			
			$this->template->add_css('assets/css/style-admin.css');
		//	$this->template->add_css('assets/css/menu.css');  // For speed testinc commented - Feb 17th 2024
			$this->template->add_css('assets/css/menu-admin.css');
			$this->template->add_js('assets/vendors/js/jquery.PrintArea.js');
		//	$this->template->add_css('assets/css/jquery-ui.css');   // For speed testinc commented - Feb 17th 2024
		//	$this->template->add_css('assets/css/glyphicon.css');	// For speed testinc commented - Feb 17th 2024
		//	$this->template->add_css('assets/css/float-label.css');	// For speed testinc commented - Feb 17th 2024
		
		$this->template->add_js('assets/vendors/js/jquery.PrintArea.js');
		$this->template->write_view('content', 'templates/pages/Events/admin-dashboard', $data, TRUE);
			$this->template->render();
		}
    }

    public function eventUPITransactionHistory()
    {
			if($this->session->userdata('user_id')==NULL)
			{
				redirect("login");
			}
		
			if($this->session->userdata('user_type') == 0)
			{
			   $data['loginType'] = "Admin";
			}
			if($this->session->userdata('user_type') == 1)
			{
			   $data['loginType'] = "Organiser";
			}
            $data['events'] = $this->events_model->getEvents();
            $data['events_dash_board_details'] = $this->events_model->getDashBoardDeatils();
            $data['categories'] = array();//$this->events_model->getCategoryOfEvent($id);
            $data['transactions']= $this->events_model->getUPIPaymentTransactionsHistory();
			$data['cartransactions']= $this->events_model->getCarUPIPaymentTransactionsHistory();
			$data['trainingprogramtransactions']= $this->events_model->getTrainingProgramUPIPaymentTransactionsHistory();
			$data['bikerallytransactions']= $this->events_model->getBikeRallyUPIPaymentTransactionsHistory();
			
			
			
		//	echo "<pre>";
		//	print_r($data['transactions']);
			$this->template->add_css('assets/css/style-admin.css');
			$this->template->add_css('assets/css/menu.css');
			$this->template->add_css('assets/css/menu-admin.css');
			$this->template->add_css('assets/css/jquery-ui.css');
			$this->template->add_css('assets/css/glyphicon.css');
			$this->template->add_css('assets/css/float-label.css');
			$this->template->write_view('content', 'templates/pages/Events/event-upi-transaction-history', $data, TRUE);
			$this->template->render();
	
    }


    public function entryForm()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect("rider");
        }
        else
        {
            $data['events'] = $this->events_model->getEvents();
    
            $this->template->write('title', 'Dashboard', TRUE);
/*
            $this->template->add_js('assets/vendors/js/tables/jquery.dataTables.min.js');
            $this->template->add_js('assets/vendors/js/tables/datatable/dataTables.bootstrap4.min.js');
            $this->template->add_js('assets/js/scripts/tables/datatables/datatable-basic.js');
    
            $this->template->add_css('assets/vendors/css/tables/datatable/dataTables.bootstrap4.min.css');

            
*/
            $this->template->add_css('assets/css/style2.css');
            $this->template->add_css('assets/css/glyphicon.css');
            $this->template->add_css('assets/css/bootstrap-select.css');
            $this->template->add_js('assets/js/bootstrap-select.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');
            $this->template->write_view('content', 'templates/pages/Events/event-entry-form', $data, TRUE);
            $this->template->render();
        }
    }
    public function UpcomingEvents()
    {
       /* if($this->session->userdata('user_id')!='')
        {
            $this->template->write('title', "Profile Page", TRUE);
            $data['title']= "Profile Page";
            $this->template->add_css('assets/css/style1.css');
                  
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');
              
            $this->template->write_view('content', 'templates/pages/Events/upcoming-events', $data, TRUE);
            $this->template->render();
        }
        else
        {
            redirect(base_url('rider'));
        }*/
		
		if($this->session->userdata('user_id')!='')
	    	{

	        	$data['participants'] = $this->participants_model->getSingleParticipants($this->session->userdata('user_id'));
				$data['history_dtls']= $this->participants_model->getPerformanceHistory($this->session->userdata('user_id'));
				/*$data['events'] = $this->participants_model->getEventsToBeAdded($this->session->userdata('user_id'));
				$data['eventsAdded'] = $this->participants_model->getEventsAdded($this->session->userdata('user_id'));
				$data['carEvents'] = $this->participants_model->getCarEventsToBeAdded($this->session->userdata('user_id'));*/
				
				
				$data['events'] = $this->participants_model->getEventsToBeAdded($this->session->userdata('user_id'));
				$data['eventsAdded'] = $this->participants_model->getEventsAdded($this->session->userdata('user_id'));
			    $data['eventsExtraDetails'] = $this->participants_model->getEventsExtraDetails();
				$data['bikerallyEvents'] = $this->participants_model->getBikeRallyEventsToBeAdded($this->session->userdata('user_id'));
				$data['bikerallyEeventsAdded'] = $this->participants_model->getBikeRallyEventsAdded($this->session->userdata('user_id'));
				$data['carEvents'] = $this->participants_model->getCarEventsToBeAdded($this->session->userdata('user_id'));
				$data['carEeventsAdded'] = $this->participants_model->getCarEventsAdded($this->session->userdata('user_id'));
				
				
				
				
				
		        $this->template->write('title', "Profile Page", TRUE);
				$data['title']= "Profile Page";
				$this->template->add_css('assets/css/style1.css');
				      
				$this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
				$this->template->add_js('assets/js/form/jquery.easing.min.js');
				$this->template->add_js('assets/js/form/script.js');
				  
		        $this->template->write_view('content', 'templates/pages/Participants/upcoming-events', $data, TRUE);
				
		        $this->template->render();
	    	}
	    	else
	    	{
	    		redirect(base_url('rider'));
	    	}


    }
    

    public function carEvents()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect("login");
        }
        else
        {
            $data['events'] = $this->events_model->getCarEvents();
    
            $this->template->write('title', 'All Car Events', TRUE);
        /*
            $template="'This is the HTML message body <b>in bold!</b>'";
            $toMail="<EMAIL>";
            $mailSubj="This is a test mail";
            $this->mailWithSMTP($template,$mailSubj,$toMail);   
        */

        
        /*        

            $this->load->library('email');
             
            $config['protocol'] = 'mail';
            $this->email->initialize($config);
            $this->email->set_newline("\r\n"); 
            $subject = 'This is a test';
            $message = '<p>This message has been sent for testing purposes.Sorry for the inconvenient.</p>';

            // Get full html:
            $body = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
            <html xmlns="http://www.w3.org/1999/xhtml">
            <head>
                <meta http-equiv="Content-Type" content="text/html; charset=' . strtolower(config_item('charset')) . '" />
                <title>' . html_escape($subject) . '</title>
                <style type="text/css">
                    body {
                        font-family: Arial, Verdana, Helvetica, sans-serif;
                        font-size: 16px;
                    }
                </style>
            </head>
            <body>
            ' . $message . '
            </body>
            </html>';
            // Also, for getting full html you may use the following internal method:
            //$body = $this->email->full_html($subject, $message);

            $result = $this->email
                ->from('<EMAIL>','MotorsportsApp')
                ->reply_to('<EMAIL>')    // Optional, an account where a human being reads.
                ->to('<EMAIL>')
                ->subject($subject)
                ->message($body)
                ->send();

            var_dump($result);
            echo '<br />';
            echo $this->email->print_debugger();
           // $headers = "From: <EMAIL>" . "\r\n";

//mail("<EMAIL>",$subject,$message,$headers);
            exit;        
        */
            $this->template->add_js('assets/vendors/js/tables/jquery.dataTables.min.js');
            $this->template->add_js('assets/vendors/js/tables/datatable/dataTables.bootstrap4.min.js');
            $this->template->add_js('assets/js/scripts/tables/datatables/datatable-basic.js');
    
            $this->template->add_css('assets/vendors/css/tables/datatable/dataTables.bootstrap4.min.css');
    
            $this->template->write_view('content', 'templates/pages/Events/list-car-events', $data, TRUE);
            $this->template->render();
        }
    }


    public function addEvents()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $data['title'] = 'Add New Events';
            $data['participants'] = 'hello';
            
            //$data['languages'] = $this->events_model->getLanguages();
    
            $this->template->write('title', 'Add Events', TRUE);
            
            $this->form_validation->set_rules('events_name', 'Event Name', 'required');
            $this->form_validation->set_rules('events_venue', 'Event Venue', 'required');
            //$this->form_validation->set_rules('hour', 'Valid Time', 'required');
    
            $data['bike_category_types'] = $this->events_model->getBikeCategoryType();    
          
            if ($this->form_validation->run() == false) {
                $this->session->set_flashdata('addEventsError', 'Error in adding events');
                $this->template->write_view('content', 'templates/pages/Events/add-events', $data, TRUE);
            }
            else {
                $addEventsStatus = $this->events_model->addEvents();
                if ($addEventsStatus) {
                    $this->session->set_flashdata('addEvents', 'Events Added');
                    redirect(base_url('events'));
                } else {
                    $this->session->set_flashdata('addEventsError', 'Error in adding events');
                    $this->template->write_view('content', 'templates/pages/Events/add-events', $data, TRUE);
                }
            }
    
          /*  $this->template->add_js('assets/vendors/js/dropzone/dropzone.min.js');
            $this->template->add_js('assets/vendors/js/dropzoneFileUpload.js');
    
            $this->template->add_css('assets/vendors/css/dropzone/dropzone.min.css'); */
    
            $this->template->write_view('content', 'templates/pages/Events/add-events', $data, TRUE);
            $this->template->render();
        }
    }
    public function addCarEvents()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $data['title'] = 'Add New Car Events';
            $data['participants'] = 'hello';
            
            //$data['languages'] = $this->events_model->getLanguages();
    
            $this->template->write('title', 'Add Car Events', TRUE);
            
            $this->form_validation->set_rules('events_name', 'Event Name', 'required');
            $this->form_validation->set_rules('events_venue', 'Event Venue', 'required');
            //$this->form_validation->set_rules('hour', 'Valid Time', 'required');
    
    
          
            if ($this->form_validation->run() == false) {
                $this->session->set_flashdata('addEventsError', 'Error in adding events');
                $this->template->write_view('content', 'templates/pages/Events/add-car-events', $data, TRUE);
            }
            else {
                $addEventsStatus = $this->events_model->addCarEvents();
                if ($addEventsStatus) {
                    $this->session->set_flashdata('addCarEvents', 'Events Added');
                    redirect(base_url('events/car-events'));
                } else {
                    $this->session->set_flashdata('addEventsError', 'Error in adding events');
                    $this->template->write_view('content', 'templates/pages/Events/add-car-events', $data, TRUE);
                }
            }
    
          /*  $this->template->add_js('assets/vendors/js/dropzone/dropzone.min.js');
            $this->template->add_js('assets/vendors/js/dropzoneFileUpload.js');
    
            $this->template->add_css('assets/vendors/css/dropzone/dropzone.min.css'); */
    
            $this->template->write_view('content', 'templates/pages/Events/add-car-events', $data, TRUE);
            $this->template->render();
        }
    }

   public function editEvents($id)
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $data['title'] = 'Edit Events';
    
            $data['events'] = $this->events_model->getSingleEvents($id);
    
            $this->template->write('title', 'Edit Events', TRUE);
    
            $this->form_validation->set_rules('events_name', 'Event Name', 'required');
            $this->form_validation->set_rules('events_venue', 'Event Venue', 'required');
            //$this->form_validation->set_rules('hour', 'Valid Time', 'required');
    
            
            if ($this->form_validation->run() == false) {
                $this->session->set_flashdata('editEventsError', 'Error in updating events');
                $this->template->write_view('content', 'templates/pages/Events/edit-events', $data, TRUE);
            } else {
                $editEventsStatus = $this->events_model->editEvents($id);
                if ($editEventsStatus) {
                    $this->session->set_flashdata('editEvents', 'Events Updated');
                    redirect(base_url('events'));
                } else {
                    $this->session->set_flashdata('editEventsError', 'Error in updating events');
                    $this->template->write_view('content', 'templates/pages/Events/edit-events', $data, TRUE);
                }
            }
    
            $this->template->render();
        }
    }
    public function editCarEvents($id)
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $data['title'] = 'Edit Car Events';
    
            $data['events'] = $this->events_model->getSingleCarEvents($id);
    
            $this->template->write('title', 'Edit Car Events', TRUE);
    
            $this->form_validation->set_rules('events_name', 'Event Name', 'required');
            $this->form_validation->set_rules('events_venue', 'Event Venue', 'required');
            //$this->form_validation->set_rules('hour', 'Valid Time', 'required');
    
            
            if ($this->form_validation->run() == false) {
                $this->session->set_flashdata('editEventsError', 'Error in updating events');
                $this->template->write_view('content', 'templates/pages/Events/edit-car-events', $data, TRUE);
            } else {
                $editEventsStatus = $this->events_model->editCarEvents($id);
                if ($editEventsStatus) {
                    $this->session->set_flashdata('editEvents', 'Events Updated');
                    redirect(base_url('events/car-events'));
                } else {
                    $this->session->set_flashdata('editEventsError', 'Error in updating events');
                    $this->template->write_view('content', 'templates/pages/Events/edit-car-events', $data, TRUE);
                }
            }
    
            $this->template->render();
        }
    }    
    
    public function addToEventsVehicleMOdel()
    {
        $vehicleModels = $this->events_model->getVehicleModels();
        echo '<option value="">Select Vehicle Model</option>';
        foreach ($vehicleModels as $vehicleModel )
        {

            echo '<option value="'.$vehicleModel["vehicle_model_id"].'">'.$vehicleModel["vehicle_model_name"].'</option>';
        }

    }
    public function suggestionVehileDetails()
    {
        $keyWord = $this->input->post('keyWord');
        $participantsId = $this->input->post('participantsId');
        $vehicleDetails = $this->events_model->getSuggestionVehileDetails($participantsId,$keyWord);
        foreach($vehicleDetails as $vehicleDetail){
            echo '<li class="vehSuggestionLi" data-veh-chasis ="'.$vehicleDetail["vehicle_details_chassis"].'" data-veh-engine ="'.$vehicleDetail["vehicle_details_engine"].'" data-veh-make ="'.$vehicleDetail["vehicle_name"].'" data-veh-model ="'.$vehicleDetail["vehicle_model_name"].'"  data-veh-make-id ="'.$vehicleDetail["vehicle_details_make"].'" data-veh-model-id ="'.$vehicleDetail["vehicle_details_model"].'">'.$vehicleDetail["vehicle_details_number"].'</li>';
        }
    }    
	
	public function addToEvents($id)
    {
        if($this->session->userdata('user_id') ==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $data['title'] = 'Add To Events';
            $data['events_id']=$id;
            $this->session->userdata('user_type',2);

            if($this->session->userdata('user_type') == 2)     
			{
                $this->confirmParticipants($this->session->userdata('user_id'),$id); //$this->confirmParticipants('75',$id);
			}
            else
            {
                $data['participants'] = $this->events_model->getAllParticipantsbyEventSuccessPayment($id);
                $data['events'] = $this->events_model->getSingleEvents($id);           
				
            }
        } 
    }
	
    public function addToCarEvents($id)
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
			$data['title'] = 'Add To Car Events';
            $data['events_id']=$id;
           
		    if($this->session->userdata('user_type')==2) 
			{
             	$this->confirmCarParticipants($this->session->userdata('user_id'),$id); 
            } 
			else
            {
				$data['participants'] = $this->events_model->getAllParticipants();
				$data['events'] = $this->events_model->getSingleCarEvents($id); 
				

				$this->template->write('title', 'Add To Car Events', TRUE); 
				$this->template->add_js('assets/vendors/js/tables/jquery.dataTables.min.js');
				$this->template->add_js('assets/vendors/js/tables/datatable/dataTables.bootstrap4.min.js');
				$this->template->add_js('assets/js/scripts/tables/datatables/datatable-basic.js');
				$this->template->add_css('assets/vendors/css/tables/datatable/dataTables.bootstrap4.min.css');
				$this->template->write_view('content', 'templates/pages/Events/car-list-participants', $data, TRUE);
				$this->template->render();
            }
        }
    }    
    public function confirmParticipants($p_id,$e_id)
    {
        if($this->session->userdata('user_id') == NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
		    $data['bikeEventParticipantsSidebarList']=$this->adds_model->addsList(11);
			$data['bikeEventOrganizerSidebarList']=$this->adds_model->addsList(12);
            $data['title'] = 'Confirm Participants';
            $data['events_id']=$e_id;
            $data['participants_id']=$p_id;
            $participantsCode = array();
            $data['participants'] = $this->events_model->getSingleParticipants($p_id);
			$data['eventType'] = "Bike";
            $data['eventParticipants'] = $this->events_model->getAllParticipantsbyEventSuccessPayment($e_id);
			$data['eventPendingParticipants'] = $this->events_model->getAllParticipantsbyEventPendingPayment($e_id);
			
            $data['orderedCategoryLists'] = $this->events_model->getTypeOrderedCategoryList($e_id);// echo '<pre>';print_r($data['orderedCategoryLists']);exit;
            $participantsCode=$this->events_model->getAllParticipantsCode($e_id);
            
            if($this->input->post('removeFromEvent') == 1)
            {

                $this->removeParticipantsFromEvents($p_id,$e_id);
            }
            for($i=1;$i<=100;$i++)
            {
                $a[$i-1]=$i;
            }
            if($participantsCode == NULL )
            {
                $participantsCode['participants_code']= array();           
            }
            
            $data['participants_code'] = $participantsCode;

            
            $data['vehicles']=$this->events_model->getVehicle('Bike');
            $data['participants_bill']=$this->events_model->getParticipantsBill($p_id,$e_id);
            //$data['participants_code']=array_diff($a,$participantsCode['participants_code']); 
            //$data['participants_code'][$data['participants']['participants_code']-1]=$data['participants']['participants_code'];
            $data['events'] = $this->events_model->getSingleEvents($e_id);
			
			
			$eventsOrganizer=$data['events'];
			$eventsOrganizerId=$eventsOrganizer['events_organizer'];
			$organizerBikeEventDetails=$this->events_model->getAllEventsByOrganizer($eventsOrganizerId);
			$organizerBikeRallyEventDetails=$this->events_model->getAllBikeRallyEventsByOrganizer($eventsOrganizerId);
			$organizerCarEventDetails=$this->events_model->getAllCarEventsByOrganizer($eventsOrganizerId);
			$data['organizerEventDetails']=array_merge($organizerBikeEventDetails,$organizerBikeRallyEventDetails,$organizerCarEventDetails);
			$dates = array();
			foreach ($data['organizerEventDetails'] as $key => $row) {
				$dates[$key] = strtotime($row['events_date']);
			}
			// Sort $data based on dates in descending order
			array_multisort($dates, SORT_DESC, $data['organizerEventDetails']);
			
			

            if($data['events']==NULL)
            $data['events']['categories']= array();
            $data['participants_categories'] = $this->events_model->getParticipantsCategories($p_id,$e_id);
            // check for already registered to this event.
            $data['event_status'] = 0;
            if(empty($data['participants_categories']))
                $data['confirm_status'] = 0;
            else
            {
                $data['confirm_status'] = 1;
                $data['payments'] = $this->events_model->getPaymantsdetails($p_id,$e_id);
                $data['event_status'] = $data['participants_categories']['events_details']['status'];
            }
            $this->template->write('title', 'Confirm Participants', TRUE);  
            $this->template->add_css('assets/css/glyphicon.css');
            //$this->template->add_css('assets/css/bootstrap-select.css');
            $this->template->add_css('assets/css/seat.css');
            $this->template->add_css('assets/css/owl.carousel.css');
            $this->template->add_css('assets/css/owl.theme.default.css');


            $this->template->add_css('assets/css/style2.css');

            $this->template->add_css('assets/css/select2.min.css');        
          
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');

            //$this->template->add_js('assets/js/bootstrap-select.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
            $this->template->add_js('assets/js/vendors/owl.carousel.js');
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');

            $this->template->add_js('assets/js/participant-list.js');
            $this->template->add_js('assets/js/select2.min.js');
			
			/*echo "<pre>";
			print_r($data['events']);
			exit;*/
			
			
            // if( $data['events']['events_is_fmsci'] == 1)
            // {
                $this->template->write_view('content', 'templates/pages/Events/confirm-participants', $data, TRUE);
            // }
            // else{
            //     $this->template->write_view('content', 'templates/pages/Events/confirm-participants-with-out-fmsci', $data, TRUE);
            // }

            $this->template->render();
        }
    }
	
	
	public function confirmCarParticipants($p_id,$e_id)
    { 
        /*echo "p id = " . $p_id . "<br>";
		echo "e id = " . $e_id . "<br>";*/
		
		
		if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $data['carEventParticipantsSidebarList']=$this->adds_model->addsList(15);
			$data['carEventOrganizerSidebarList']=$this->adds_model->addsList(16);
		    $data['title'] = 'Confirm Car Participants';
            $data['events_id']=$e_id;
            $data['participants_id']=$p_id;
            $participantsCode = array();
		//	$data['eventPaymentRegInfo'] = $this->events_model->getCarEventPaymentRegInfo($e_id,$p_id);
            $data['participants'] = $this->events_model->getSingleParticipants($p_id);
			$data['eventType'] = "Car";
         //   $data['eventParticipants'] = $this->events_model->getAllParticipantsbyCarEventSuccessPaymentNew($e_id);
			$data['eventParticipants'] = $this->events_model->getAllParticipantsbyCarEventSuccessPayment($e_id);
			$data['eventPendingParticipants'] = $this->events_model->getAllParticipantsbyCarEventPendingPayment($e_id);
			
			
			
            $data['orderedCategoryLists'] = $this->events_model->getTypeOrderedCarCategoryList($e_id);
            $participantsCode=$this->events_model->getAllCarParticipantsCode($e_id); 
            if($this->input->post('removeFromEvent') == 1)
            {
                $this->removeParticipantsFromEvents($p_id,$e_id);
            }
            for($i=1;$i<=100;$i++)
            {
                $a[$i-1]=$i;
            }
            if($participantsCode == NULL )
            {
                $participantsCode['participants_code']= array();           
            }
			
			$data['participants_code'] = $participantsCode;
			 
            $data['participants_bill']=$this->events_model->getCarParticipantsBill($p_id,$e_id);
           // $data['participants_code']=array_diff($a,$participantsCode['participants_code']); orderedCategoryLists
           // $data['participants_code'][$data['participants']['participants_code']-1]=$data['participants']['participants_code'];
            $data['events'] = $this->events_model->getSingleCarEvents($e_id);
			
			$eventsOrganizer=$data['events'];
			$eventsOrganizerId=$eventsOrganizer['events_organizer'];
			$organizerBikeEventDetails=$this->events_model->getAllEventsByOrganizer($eventsOrganizerId);
			$organizerBikeRallyEventDetails=$this->events_model->getAllBikeRallyEventsByOrganizer($eventsOrganizerId);
			$organizerCarEventDetails=$this->events_model->getAllCarEventsByOrganizer($eventsOrganizerId);
			$data['organizerEventDetails']=array_merge($organizerBikeEventDetails,$organizerBikeRallyEventDetails,$organizerCarEventDetails);
			$dates = array();
			foreach ($data['organizerEventDetails'] as $key => $row) {
				$dates[$key] = strtotime($row['events_date']);
			}
			// Sort $data based on dates in descending order
			array_multisort($dates, SORT_DESC, $data['organizerEventDetails']);
			
			
			$data['orderedCategoryLists'] = $this->events_model->getTypeOrderedCarCategoryList($e_id);
			//$data['orderedCategoryLists']['categories_prices'] = [];
            if($data['events']==NULL)
            $data['events']['categories']= array();
            $data['participants_categories'] = [];//$this->events_model->getCarParticipantsCategories($p_id,$e_id);  
            // check for already registered to this event. 
            $data['event_status'] = 0;
            if(empty($data['participants_categories']))
                $data['confirm_status'] = 0;
            else
            {
                $data['confirm_status'] = 1;
                $data['payments'] = $this->events_model->getCarPaymantsdetails($p_id,$e_id);
                $data['event_status'] = $data['participants_categories']['events_details']['status'];
            }
            $this->template->write('title', 'Confirm Participants', TRUE);  

            
			 //-----
			 $this->template->add_css('assets/css/glyphicon.css');
            //$this->template->add_css('assets/css/bootstrap-select.css');
            $this->template->add_css('assets/css/seat.css');
            $this->template->add_css('assets/css/owl.carousel.css');
            $this->template->add_css('assets/css/owl.theme.default.css');


            $this->template->add_css('assets/css/style2.css');

            $this->template->add_css('assets/css/select2.min.css');        
          
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');

            //$this->template->add_js('assets/js/bootstrap-select.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
            $this->template->add_js('assets/js/vendors/owl.carousel.js');
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');

            $this->template->add_js('assets/js/participant-list.js');
            $this->template->add_js('assets/js/select2.min.js');
			//....
			
            $this->template->write_view('content', 'templates/pages/Events/confirm-car-participants', $data, TRUE);
            $this->template->render();
        }
    }
	
	
    public function viewParticipantEventDetails($e_id)
    {
        if($this->session->userdata('user_id') == NULL)
        {
            redirect(base_url('login'));
            
        }
        else
        {
            $p_id = $this->session->userdata('user_id');
            $data['title'] = 'View Event Details';
            $data['events_id']=$e_id;
            $data['participants_id']=$p_id;
            $participantsCode = array();
            $data['participants'] = $this->events_model->getSingleParticipants($p_id);
            $data['eventParticipants'] = $this->events_model->getAllParticipantsbyEvent($e_id);
            $data['orderedCategoryLists'] = $this->events_model->getTypeOrderedCategoryList($e_id);
            $participantsCode=$this->events_model->getAllParticipantsCode($e_id);
            if($this->input->post('removeFromEvent') == 1)
            {

                $this->removeParticipantsFromEvents($p_id,$e_id);
            }
            for($i=1;$i<=100;$i++)
            {
                $a[$i-1]=$i;
            }
            if($participantsCode == NULL )
            {
                $participantsCode['participants_code']= array();           
            }
            
            $data['participants_code'] = $participantsCode;

            
            $data['vehicles']=$this->events_model->getVehicle('Bike');
            $data['participants_bill']=$this->events_model->getParticipantsBill($p_id,$e_id);
            //$data['participants_code']=array_diff($a,$participantsCode['participants_code']);
            //$data['participants_code'][$data['participants']['participants_code']-1]=$data['participants']['participants_code'];
            $data['events'] = $this->events_model->getSingleEvents($e_id);
            $data['participants_vehicle_details']=$this->events_model->getParticipantsVehicles($p_id,$e_id);
            if($data['events']==NULL)
            $data['events']['categories']= array();
            $data['participants_categories'] = $this->events_model->getParticipantsCategories($p_id,$e_id);
            // check for already registered to this event.
            $data['event_status'] = 0;
            if(empty($data['participants_categories']))
                $data['confirm_status'] = 0;
            else
            {
                $data['confirm_status'] = 1;
                $data['payments'] = $this->events_model->getPaymantsdetails($p_id,$e_id);
                $data['event_status'] = $data['participants_categories']['events_details']['status'];
            }
            $data['event_entry_details'] = $this->events_model->getEventEntryDetails($p_id,$e_id);
            $this->template->write('title', 'Confirm Participants', TRUE);  


            
            $this->template->add_css('assets/css/glyphicon.css');
            //$this->template->add_css('assets/css/bootstrap-select.css');
            $this->template->add_css('assets/css/seat.css');
            $this->template->add_css('assets/css/owl.carousel.css');
            $this->template->add_css('assets/css/owl.theme.default.css');


            $this->template->add_css('assets/css/style2.css');

            $this->template->add_css('assets/css/select2.min.css');        
          
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');

            //$this->template->add_js('assets/js/bootstrap-select.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
            $this->template->add_js('assets/js/vendors/owl.carousel.js');
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');

            $this->template->add_js('assets/js/participant-list.js');
            $this->template->add_js('assets/js/select2.min.js');
            $this->template->write_view('content', 'templates/pages/Events/view-participant-event-details', $data, TRUE);

            $this->template->render();
        }
    }


 public function updatePendingPayment($e_id)
    {
        if($this->session->userdata('user_id') == NULL)
        {
            redirect(base_url('login'));
            
        }
        else
        {
            $data['title'] = 'Update Payment';
            $data['events_id']=$e_id;
            $p_id = $this->session->userdata('user_id');

            $data['participants_id']=$p_id;
            $data['participants'] = $this->events_model->getSingleParticipants($p_id);


            
            $data['participants_bill']=$this->events_model->getParticipantsBill($p_id,$e_id);
            //$data['participants_code']=array_diff($a,$participantsCode['participants_code']);
            //$data['participants_code'][$data['participants']['participants_code']-1]=$data['participants']['participants_code'];
            $data['events'] = $this->events_model->getSingleEvents($e_id);

            $data['payments'] = $this->events_model->getPaymantsdetails($p_id,$e_id);
            $this->template->write('title', 'Update Payment', TRUE);  


            
            $this->template->add_css('assets/css/glyphicon.css');
            //$this->template->add_css('assets/css/bootstrap-select.css');
            $this->template->add_css('assets/css/seat.css');
            $this->template->add_css('assets/css/owl.carousel.css');
            $this->template->add_css('assets/css/owl.theme.default.css');


            $this->template->add_css('assets/css/style2.css');

          
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');

            //$this->template->add_js('assets/js/bootstrap-select.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
            $this->template->add_js('assets/js/vendors/owl.carousel.js');
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');

            $this->template->write_view('content', 'templates/pages/Events/update-pending-payment', $data, TRUE);


            $this->template->render();
        }
    }

    public function confirmPendingPayment()
    {

        
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {

            $this->form_validation->set_rules('events_id', 'Event Id', 'required');
            $this->form_validation->set_rules('participants_id', 'Participants Id', 'required');
            //$this->form_validation->set_rules('category_cnt', 'Category Count', 'required');
            
            if ($this->form_validation->run() == false) {

                $this->session->set_flashdata('confirmEventsError', 'Error in confirming events.Please fill the mandatory fields.');
               $path = 'events/update-pending-payment/'.$this->input->post('events_id');
                redirect($path);
            }
            else {


                $updateEventsStatus = $this->events_model->updateParticipantsPayments();
                $statusArray = explode('$#',$updateEventsStatus);
                $dataPay['title'] = 'Proceed with Payment mode ';
                $dataPay['payment_mode'] = $this->input->post('payment_mode');
                $dataPay['payments_dtl']=array(
                "purpose" => $statusArray[0],
                "amount" => $statusArray[1],
                "send_email" => true,
                "email" => $this->input->post('participants_email'),
                'phone' => $this->input->post('phone'),
                'buyer_name' => $this->input->post('buyer_name'), 
                'events_id'=> $this->input->post('events_id'),                     
                "redirect_url" => base_url('events/do-after-payment')                       
                );

                // store data to flashdata
                $this->session->set_flashdata('paymentData',$dataPay);
                // after storing redirect it to the controller
                redirect('instamojo/insta-mojo-create');


                }
        }
        
    }  

    public function upiPayment()
    {
        if($this->session->userdata('user_id') == NULL)
        {
            redirect(base_url('login'));
            
        }
        else
        {
            $data['title'] = 'UPI Payment';
            $p_id = $this->session->userdata('user_id');


            //$data['participants_code']=array_diff($a,$participantsCode['participants_code']);
            //$data['participants_code'][$data['participants']['participants_code']-1]=$data['participants']['participants_code'];

            $this->template->write('title', 'Update Payment', TRUE);  


            $data['advtDetails']=$this->adds_model->addsList(35);


            $this->template->add_css('assets/css/style2.css');

          
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');

            //$this->template->add_js('assets/js/bootstrap-select.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');

            $this->template->write_view('content', 'templates/pages/Events/upi-payment', $data, TRUE);


            $this->template->render();
        }
    }
	
	
    public function paymentProcess($e_id)
    {
        if($this->session->userdata('user_id') == NULL)
        {
            redirect(base_url('login'));
            
        }
        else
        {
			$data['bikePaymentProcess']=$this->adds_model->addsList(25);
            $this->form_validation->set_rules('upiTransactionId', 'UPI Transaction Id', 'required');
            //$this->form_validation->set_rules('category_cnt', 'Category Count', 'required');
            $data['e_id'] = $e_id;
            $p_id = $this->session->userdata('user_id');
            $data['checkPaymentWithUPI'] = $this->events_model->checkPaymentWithUPI($e_id,$p_id);
			
		
            $data['events'] = $this->events_model->getSingleEvents($e_id);
			$eventsOrganizer=$data['events'];
			$eventsOrganizerId=$eventsOrganizer['events_organizer'];
			$organizerBikeEventDetails=$this->events_model->getAllEventsByOrganizer($eventsOrganizerId);
			$organizerBikeRallyEventDetails=$this->events_model->getAllBikeRallyEventsByOrganizer($eventsOrganizerId);
			$organizerCarEventDetails=$this->events_model->getAllCarEventsByOrganizer($eventsOrganizerId);
			$data['organizerEventDetails']=array_merge($organizerBikeEventDetails,$organizerBikeRallyEventDetails,$organizerCarEventDetails);
			$dates = array();
			foreach ($data['organizerEventDetails'] as $key => $row)
			{
			   $dates[$key] = strtotime($row['events_date']);
			}
			// Sort $data based on dates in descending order
			array_multisort($dates, SORT_DESC, $data['organizerEventDetails']);	
			
			
            $data['eventPaymentRegInfo'] = $this->events_model->getEventPaymentRegInfo($e_id,$p_id);
            $data['eventParticipants'] = $this->events_model->getAllParticipantsbyEventSuccessPayment($e_id);
			$data['eventPendingParticipants'] = $this->events_model->getAllParticipantsbyEventPendingPayment($e_id);
			
		//	exit;

            if ($this->form_validation->run() == false) {

                $this->session->set_flashdata('upiTransactionError', 'Error in UPI Transaction Id submission.Please fill the mandatory fields.');
            }
            else {           
                $data['updatePaymentWithUPI'] = $this->events_model->updatePaymentWithUPI();
                $path = 'events/payment-process/'.$e_id;
                redirect(base_url($path));
            }
            $data['title'] = 'Payment Process';
            $this->template->write('title', 'Payment Process', TRUE);  
            $this->template->add_css('assets/css/style2.css');
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');
            $this->template->write_view('content', 'templates/pages/Events/payment-process', $data, TRUE);
            $this->template->render();
       
        }
    }
    public function carPaymentProcess($e_id)
    {
        if($this->session->userdata('user_id') == NULL)
        {
            redirect(base_url('login'));
            
        }
        else
        {
			$data['carPaymentProcess']=$this->adds_model->addsList(27);
            $this->form_validation->set_rules('upiTransactionId', 'UPI Transaction Id', 'required');
            //$this->form_validation->set_rules('category_cnt', 'Category Count', 'required');
            $data['e_id'] = $e_id;
            $p_id = $this->session->userdata('user_id');
            $data['checkPaymentWithUPI'] = $this->events_model->checkCarPaymentWithUPI($e_id,$p_id);
            $data['events'] = $this->events_model->getSingleCarEvents($e_id);
			$eventsOrganizer=$data['events'];
			$eventsOrganizerId=$eventsOrganizer['events_organizer'];
			$organizerBikeEventDetails=$this->events_model->getAllEventsByOrganizer($eventsOrganizerId);
			$organizerBikeRallyEventDetails=$this->events_model->getAllBikeRallyEventsByOrganizer($eventsOrganizerId);
			$organizerCarEventDetails=$this->events_model->getAllCarEventsByOrganizer($eventsOrganizerId);
			$data['organizerEventDetails']=array_merge($organizerBikeEventDetails,$organizerBikeRallyEventDetails,$organizerCarEventDetails);
			$dates = array();
			foreach ($data['organizerEventDetails'] as $key => $row) {
			  $dates[$key] = strtotime($row['events_date']);
			}
			// Sort $data based on dates in descending order
			array_multisort($dates, SORT_DESC, $data['organizerEventDetails']);
            $data['eventPaymentRegInfo'] = $this->events_model->getCarEventPaymentRegInfo($e_id,$p_id);
            $data['eventParticipants'] = $this->events_model->getAllParticipantsbyCarEventSuccessPayment($e_id);
			$data['eventPendingParticipants'] = $this->events_model->getAllParticipantsbyCarEventPendingPayment($e_id);
			
			
			$data['participants_id']=$this->session->userdata('user_id');
			$data['event_entry_details'] = $this->events_model->getCarEventEntryDetails($data['participants_id'],$e_id);
			
			

            if ($this->form_validation->run() == false) {

                $this->session->set_flashdata('upiTransactionError', 'Error in UPI Transaction Id submission.Please fill the mandatory fields.');
            }
            else {           
                $data['updatePaymentWithUPI'] = $this->events_model->updateCarPaymentWithUPI();
                $path = 'events/car-payment-process/'.$e_id;
                redirect(base_url($path));
            }
			$data['eventType']="Car";
            $data['title'] = 'Payment Process';
            $this->template->write('title', 'Payment Process', TRUE);  
            $this->template->add_css('assets/css/style2.css');
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');
            $this->template->write_view('content', 'templates/pages/Events/car-payment-process', $data, TRUE);
            $this->template->render();
       
        }
    }


    public function paymentConfirm($e_id)
    {
        if($this->session->userdata('user_id') == NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
			$data['paymentConfirm']=$this->adds_model->addsList(28);
            $data['title'] = 'Payment Confirm';
            $p_id = $this->session->userdata('user_id');
            $data['eventPaymentRegInfo'] = $this->events_model->getEventPaymentRegInfo($e_id,$p_id);
			
			/*print_r($data['eventPaymentRegInfo']);
			exit;*/
			 $data['eventParticipants'] = $this->events_model->getAllParticipantsbyEventSuccessPayment($e_id);
			$data['eventPendingParticipants'] = $this->events_model->getAllParticipantsbyEventPendingPayment($e_id);
            $data['eventType'] = "Bike";
			$data['events'] = $this->events_model->getSingleEvents($e_id);
			
			$eventsOrganizer=$data['events'];
			$eventsOrganizerId=$eventsOrganizer['events_organizer'];
			$organizerBikeEventDetails=$this->events_model->getAllEventsByOrganizer($eventsOrganizerId);
			$organizerBikeRallyEventDetails=$this->events_model->getAllBikeRallyEventsByOrganizer($eventsOrganizerId);
			$organizerCarEventDetails=$this->events_model->getAllCarEventsByOrganizer($eventsOrganizerId);
			$data['organizerEventDetails']=array_merge($organizerBikeEventDetails,$organizerBikeRallyEventDetails,$organizerCarEventDetails);
			
			$dates = array();
			foreach ($data['organizerEventDetails'] as $key => $row)
			{
			   $dates[$key] = strtotime($row['events_date']);
			}
			// Sort $data based on dates in descending order
			array_multisort($dates, SORT_DESC, $data['organizerEventDetails']);	

			$data['events_id']=$e_id;
			// $data['events'] = $this->events_model->getSingleEvents($e_id);
            // print_r($data['eventPaymentRegInfo']);exit;
            $this->template->write('title', 'Payment Confirm', TRUE);  
            $this->template->add_css('assets/css/style2.css');          
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js'); 
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');
			
			$data['participants_id']=$p_id;
			$data['participants_events_id']=$e_id;
			

            $this->template->write_view('content', 'templates/pages/Events/payment-confirm', $data, TRUE);


            $this->template->render();
        }
    }
	
	public function participantPaymentConfirm($e_id,$p_id)
    {
        if($this->session->userdata('user_id') == NULL)
        {
            redirect(base_url('login'));
            
        }
        else
        {
            $data['title'] = 'Payment Confirm';
			$data['paymentConfirm']=$this->adds_model->addsList(28);
         //   $p_id = $this->session->userdata('user_id');
            $data['eventPaymentRegInfo'] = $this->events_model->getEventPaymentRegInfo($e_id,$p_id);
			
			/*print_r($data['eventPaymentRegInfo']);
			exit;*/
            $data['eventParticipants'] = $this->events_model->getAllParticipantsbyEventSuccessPayment($e_id);
            $data['eventType'] = "Bike";
			$data['events'] = $this->events_model->getSingleEvents($e_id);
			$data['events_id']=$e_id;
			// $data['events'] = $this->events_model->getSingleEvents($e_id);
            // print_r($data['eventPaymentRegInfo']);exit;
            $this->template->write('title', 'Payment Confirm', TRUE);  
            $this->template->add_css('assets/css/style2.css');          
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js'); 
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');
			
			$data['participants_id']=$p_id;
			$data['participants_events_id']=$e_id;
			

            $this->template->write_view('content', 'templates/pages/Events/payment-confirm', $data, TRUE);


            $this->template->render();
        }
    }
	
	
    public function carPaymentConfirm($e_id)
    {
        if($this->session->userdata('user_id') == NULL)
        {
            redirect(base_url('login'));
            
        }
        else
        {
			$data['paymentConfirm']=$this->adds_model->addsList(30);
            $data['title'] = 'Payment Confirm';
            $p_id = $this->session->userdata('user_id');
            $data['eventPaymentRegInfo'] = $this->events_model->getCarEventPaymentRegInfo($e_id,$p_id);
			$data['eventParticipants'] = $this->events_model->getAllParticipantsbyCarEventSuccessPayment($e_id);
			$data['eventPendingParticipants'] = $this->events_model->getAllParticipantsbyCarEventPendingPayment($e_id);
			$data['events'] = $this->events_model->getSingleCarEvents($e_id);
			
			$eventsOrganizer=$data['events'];
			$eventsOrganizerId=$eventsOrganizer['events_organizer'];
			$organizerBikeEventDetails=$this->events_model->getAllEventsByOrganizer($eventsOrganizerId);
			$organizerBikeRallyEventDetails=$this->events_model->getAllBikeRallyEventsByOrganizer($eventsOrganizerId);
			$organizerCarEventDetails=$this->events_model->getAllCarEventsByOrganizer($eventsOrganizerId);
			$data['organizerEventDetails']=array_merge($organizerBikeEventDetails,$organizerBikeRallyEventDetails,$organizerCarEventDetails);
			$dates = array();
			foreach ($data['organizerEventDetails'] as $key => $row) {
			  $dates[$key] = strtotime($row['events_date']);
			}
			// Sort $data based on dates in descending order
			array_multisort($dates, SORT_DESC, $data['organizerEventDetails']);

			
			
			
            $data['eventType'] = "Car";
            // print_r($data['eventPaymentRegInfo']);exit;
            $this->template->write('title', 'Payment Confirm', TRUE);  
            $this->template->add_css('assets/css/style2.css');          
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js'); 
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');
			

			$data['participants_id']=$p_id;
			$data['participants_events_id']=$e_id;
			$data['events_id']=$e_id;

            $this->template->write_view('content', 'templates/pages/Events/payment-confirm', $data, TRUE);


            $this->template->render();
        }
    }

    public function paymentUPIProcessing($e_id)
    {
        if($this->session->userdata('user_id') == NULL)
        {
            redirect(base_url('login'));
            
        }
        else
        {
            $data['title'] = 'Payment UPI Processing';
            $p_id = $this->session->userdata('user_id');
            $data['eventType'] = "Bike";

            
            //$data['participants_code']=array_diff($a,$participantsCode['participants_code']);
            $data['eventPaymentRegInfo'] = $this->events_model->getEventPaymentRegInfo($e_id,$p_id);
            $data['eventParticipants'] = $this->events_model->getAllParticipantsbyEventSuccessPayment($e_id);
			$data['eventPendingParticipants'] = $this->events_model->getAllParticipantsbyEventPendingPayment($e_id);

            // print_r($data['eventPaymentRegInfo']);exit;
			$data['events'] = $this->events_model->getSingleEvents($e_id);
			$eventsOrganizer=$data['events'];
			$eventsOrganizerId=$eventsOrganizer['events_organizer'];
			$organizerBikeEventDetails=$this->events_model->getAllEventsByOrganizer($eventsOrganizerId);
			$organizerBikeRallyEventDetails=$this->events_model->getAllBikeRallyEventsByOrganizer($eventsOrganizerId);
			$organizerCarEventDetails=$this->events_model->getAllCarEventsByOrganizer($eventsOrganizerId);
			$data['organizerEventDetails']=array_merge($organizerBikeEventDetails,$organizerBikeRallyEventDetails,$organizerCarEventDetails);
			$dates = array();
			foreach ($data['organizerEventDetails'] as $key => $row)
			{
			   $dates[$key] = strtotime($row['events_date']);
			}
			// Sort $data based on dates in descending order
			array_multisort($dates, SORT_DESC, $data['organizerEventDetails']);	


            $this->template->write('title', 'Payment UPI Processing', TRUE);  


            


            $this->template->add_css('assets/css/style2.css');

          
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');

            //$this->template->add_js('assets/js/bootstrap-select.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');

            $this->template->write_view('content', 'templates/pages/Events/payment-upi-processing', $data, TRUE);


            $this->template->render();
        }
    }

    public function carPaymentUPIProcessing($e_id)
    {
        if($this->session->userdata('user_id') == NULL)
        {
            redirect(base_url('login'));
            
        }
        else
        {
			$data['paymentUpiProcessing']=$this->adds_model->addsList(31);
            $data['title'] = 'Payment UPI Processing';
            $p_id = $this->session->userdata('user_id');
            $data['eventType'] = "Car";

            $data['events'] = $this->events_model->getSingleCarEvents($e_id);
			$eventsOrganizer=$data['events'];
			$eventsOrganizerId=$eventsOrganizer['events_organizer'];
			$organizerBikeEventDetails=$this->events_model->getAllEventsByOrganizer($eventsOrganizerId);
			$organizerBikeRallyEventDetails=$this->events_model->getAllBikeRallyEventsByOrganizer($eventsOrganizerId);
			$organizerCarEventDetails=$this->events_model->getAllCarEventsByOrganizer($eventsOrganizerId);
			$data['organizerEventDetails']=array_merge($organizerBikeEventDetails,$organizerBikeRallyEventDetails,$organizerCarEventDetails);
			$dates = array();
			foreach ($data['organizerEventDetails'] as $key => $row)
			{
			   $dates[$key] = strtotime($row['events_date']);
			}
			// Sort $data based on dates in descending order
			array_multisort($dates, SORT_DESC, $data['organizerEventDetails']);	

			//$data['orderedCategoryLists']['categories_prices'] = [];
			//echo '<pre>';print_r($data['orderedCategoryLists']); exit;
            if($data['events']==NULL)
            $data['events']['categories']= array();        
            //$data['participants_code']=array_diff($a,$participantsCode['participants_code']);
            $data['eventPaymentRegInfo'] = $this->events_model->getCarEventPaymentRegInfo($e_id,$p_id);
            $data['eventParticipants'] = $this->events_model->getAllParticipantsbyCarEventSuccessPayment($e_id);
			$data['eventPendingParticipants'] = $this->events_model->getAllParticipantsbyCarEventPendingPayment($e_id);
            // print_r($data['eventPaymentRegInfo']);exit;

            $this->template->write('title', 'Payment UPI Processing', TRUE);  


            


            $this->template->add_css('assets/css/style2.css');

          
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');

            //$this->template->add_js('assets/js/bootstrap-select.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');

            $this->template->write_view('content', 'templates/pages/Events/payment-upi-processing', $data, TRUE);     /////////////////////////////////////////////


            $this->template->render();
        }
    }
	
	
	public function trainingprogramPaymentUPIProcessing($e_id)
    {
        if($this->session->userdata('user_id') == NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
			$data['paymentUpiProcessing']=$this->adds_model->addsList(31);
            $data['title'] = 'Payment UPI Processing';
            $p_id = $this->session->userdata('user_id');
            $data['eventType'] = "Training Program";
            $data['events'] = $this->events_model->getSingleTrainingProgramEvents($e_id);
			$eventsOrganizer=$data['events'];
			$eventsOrganizerId=$eventsOrganizer['events_organizer'];
			$organizerBikeEventDetails=$this->events_model->getAllEventsByOrganizer($eventsOrganizerId);
			$organizerBikeRallyEventDetails=$this->events_model->getAllBikeRallyEventsByOrganizer($eventsOrganizerId);
			$organizerCarEventDetails=$this->events_model->getAllCarEventsByOrganizer($eventsOrganizerId);
			$data['organizerEventDetails']=array_merge($organizerBikeEventDetails,$organizerBikeRallyEventDetails,$organizerCarEventDetails);
			$dates = array();
			foreach ($data['organizerEventDetails'] as $key => $row)
			{
			   $dates[$key] = strtotime($row['events_date']);
			}
			// Sort $data based on dates in descending order
			array_multisort($dates, SORT_DESC, $data['organizerEventDetails']);	

            if($data['events']==NULL)
            $data['events']['categories']= array();        
            //$data['participants_code']=array_diff($a,$participantsCode['participants_code']);
            $data['eventPaymentRegInfo'] = $this->events_model->getTrainingProgramEventPaymentRegInfo($e_id,$p_id);
            $data['eventParticipants'] = $this->events_model->getAllParticipantsbyTrainingProgramEventSuccessPayment($e_id);
		//	print_r($data['eventParticipants']);
			
			$data['participants_id']=$p_id;
			$data['participants_events_id']=$e_id;
			$data['events_id']=$e_id;

			
			$data['eventPendingParticipants'] = $this->events_model->getAllParticipantsbyTrainingProgramEventPendingPayment($e_id);
            $this->template->write('title', 'Payment UPI Processing', TRUE);  
            $this->template->add_css('assets/css/style2.css');
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');
            $this->template->write_view('content', 'templates/pages/Events/payment-upi-processing', $data, TRUE);
		  // $this->template->write_view('content', 'templates/pages/Events/payment-confirm', $data, TRUE);
            $this->template->render();
        }
    }
	
	
	public function ticketPaymentUPIProcessing($e_id)
    {
        if($this->session->userdata('user_id') == NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
			$data['paymentUpiProcessing']=$this->adds_model->addsList(31);
            $data['title'] = 'Payment UPI Processing';
            $p_id = $this->session->userdata('user_id');
            $data['eventType'] = "Ticket Events";
            $data['events'] = $this->events_model->getSingleTicketEvents($e_id);
			$eventsOrganizer=$data['events'];
			$eventsOrganizerId=$eventsOrganizer['events_organizer'];
			$organizerBikeEventDetails=$this->events_model->getAllEventsByOrganizer($eventsOrganizerId);
			$organizerBikeRallyEventDetails=$this->events_model->getAllBikeRallyEventsByOrganizer($eventsOrganizerId);
			$organizerCarEventDetails=$this->events_model->getAllCarEventsByOrganizer($eventsOrganizerId);
			$data['organizerEventDetails']=array_merge($organizerBikeEventDetails,$organizerBikeRallyEventDetails,$organizerCarEventDetails);
			$dates = array();
			foreach ($data['organizerEventDetails'] as $key => $row)
			{
			   $dates[$key] = strtotime($row['events_date']);
			}
			// Sort $data based on dates in descending order
			array_multisort($dates, SORT_DESC, $data['organizerEventDetails']);	

            if($data['events']==NULL)
            $data['events']['categories']= array();        
            //$data['participants_code']=array_diff($a,$participantsCode['participants_code']);
            $data['eventPaymentRegInfo'] = $this->events_model->getTicketEventPaymentRegInfo($e_id,$p_id);
            $data['eventParticipants'] = $this->events_model->getAllParticipantsbyTicketEventSuccessPayment($e_id);
		//	print_r($data['eventParticipants']);
			
			$data['participants_id']=$p_id;
			$data['participants_events_id']=$e_id;
			$data['events_id']=$e_id;

			
			$data['eventPendingParticipants'] = $this->events_model->getAllParticipantsbyTrainingProgramEventPendingPayment($e_id);
            $this->template->write('title', 'Payment UPI Processing', TRUE);  
            $this->template->add_css('assets/css/style2.css');
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');
            $this->template->write_view('content', 'templates/pages/Events/ticket-payment-upi-processing', $data, TRUE);
		  // $this->template->write_view('content', 'templates/pages/Events/payment-confirm', $data, TRUE);
            $this->template->render();
        }
    }
    
    
    public function addParticipantsToEvents()
    {

        
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $this->form_validation->set_rules('events_id', 'Event Id', 'required');
            $this->form_validation->set_rules('participants_id', 'Participants Id', 'required');
            $this->form_validation->set_rules('categoryIds', 'Category Ids', 'required');
            //$this->form_validation->set_rules('category_cnt', 'Category Count', 'required');
            //echo $this->form_validation->run();exit;
            if ($this->form_validation->run() == false) {

                $this->session->set_flashdata('confirmEventsError', 'Error in confirming events.Please fill the mandatory fields.');
               $path = '/events/add-to-events/'.$this->input->post('events_id');
                redirect($path);
            }
            else {

                    if($this->input->post('removeFromEvent')== 1)
                        $addEventsStatus = $this->events_model->removeParticipantsFromEvents($this->input->post('participants_id'),$this->input->post('events_id'));
                    else
                    $addEventsStatus = $this->events_model->addParticipantsToEvents();
					
				//	echo "add event status = " . $addEventsStatus;
					

                    $data['events'] = $this->events_model->getSingleEvents($this->input->post('events_id'));
					
				//	print_r($data['events']);
					
					
					
					
					$eventsDet = $this->events_model->getSingleEvents($this->input->post('events_id'));
					
					// print_r($eventsDet);
					
					if($eventsDet)
					{
							if($eventsDet['api_key'] != "")
							{
							   $api_key=$eventsDet['api_key'];
							}
							else
							{
							   $api_key=$this->api_key;
							}
							if($eventsDet['auth_token'] != "")
							{
							   $auth_token=$eventsDet['auth_token'];
							}
							else
							{
							   $auth_token=$this->auth_token;
							}
					}
					else
					{
							$api_key=$this->api_key;
							$auth_token=$this->auth_token;
					}
		
					
					
					
                    if($this->session->userdata('user_type')!=2)
                    {
                        if($this->input->post('print_status')==1)
                        {
                            redirect('participants/event-entry-form-detailed-shortcut/'.$this->input->post('participants_id').'/'.$this->input->post('events_id'));
                        }
                        else
                        {
                            if ($addEventsStatus) {
                                if($this->input->post('removeFromEvent')== 1)
                                    $this->session->set_flashdata('confirmEvents', 'Removed from Event');
                                else
                                $this->session->set_flashdata('confirmEvents', 'Events Confirmed');
                                redirect(base_url('events'));
                            } else {
                                    if($this->input->post('removeFromEvent')== 1)
                                        $this->session->set_flashdata('confirmEventsError', 'Error in removing from events');
                                    else
                                        $this->session->set_flashdata('confirmEventsError', 'Error in confirming events');
                                    redirect(base_url('events'));
                            }
                        }
                    }
                    else
                    {
						$statusArray = explode('$#',$addEventsStatus);
                        if($statusArray[1]==0)
                        {
                            redirect('events/payment-confirm/'.$this->input->post('events_id'));
                        }
                       
                        $dataPay['title'] = 'Proceed with Payment mode ';
                        $dataPay['payment_mode'] = $this->input->post('payment_mode');
                        $dataPay['payments_dtl']=array(
                        "purpose" => urlencode($statusArray[0]),
                        "amount" => $statusArray[1],
                        "send_email" => true,
                        "email" => $this->input->post('participants_email'),
                        'phone' => $this->input->post('phone'),
                        'buyer_name' => $this->input->post('buyer_name'),     
                        'events_id'=> $this->input->post('events_id'), 
						'api_key' => $api_key,
						'auth_token' =>  $auth_token,           
                        "redirect_url" => base_url('events/do-after-payment')                       
                        );
					//	echo "<pre>";
					//	print_r($dataPay);
					//	exit;

                        // store data to flashdata
                        $this->session->set_flashdata('paymentData',$dataPay);
                        // after storing redirect it to the controller
                        redirect('instamojo/insta-mojo-create');
                    }
                }
        }
        
    }   
    
    public function addParticipantsToCarEvents()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
		
            $this->form_validation->set_rules('events_id', 'Event Id', 'required');
            $this->form_validation->set_rules('participants_id', 'Participants Id', 'required');
            $this->form_validation->set_rules('category_cnt', 'Category', 'required');
    
            
            if ($this->form_validation->run() == false)
			{
                $this->session->set_flashdata('confirmEventsError', 'Error in confirming events');
                redirect('events/car-events');
            }
            else {
                    if($this->input->post('removeFromEvent')== 1)
                        $addEventsStatus = $this->events_model->removeCarParticipantsFromEvents($this->input->post('participants_id'),$this->input->post('events_id'));
                    else
						$addEventsStatus = $this->events_model->addParticipantsToCarEvents();
						
					

                    $data['events'] = $this->events_model->getSingleCarEvents($this->input->post('events_id'));
					
					$eventsOrganizer=$data['events'];
					$eventsOrganizerId=$eventsOrganizer['events_organizer'];
					$organizerBikeEventDetails=$this->events_model->getAllEventsByOrganizer($eventsOrganizerId);
					$organizerBikeRallyEventDetails=$this->events_model->getAllBikeRallyEventsByOrganizer($eventsOrganizerId);
					$organizerCarEventDetails=$this->events_model->getAllCarEventsByOrganizer($eventsOrganizerId);
					$data['organizerEventDetails']=array_merge($organizerBikeEventDetails,$organizerBikeRallyEventDetails,$organizerCarEventDetails);
					$dates = array();
					foreach ($data['organizerEventDetails'] as $key => $row) {
					  $dates[$key] = strtotime($row['events_date']);
					}
					// Sort $data based on dates in descending order
					array_multisort($dates, SORT_DESC, $data['organizerEventDetails']);

					
					
					$eventsDet = $this->events_model->getSingleCarEvents($this->input->post('events_id'));
					
					
					if($eventsDet)
					{
							if($eventsDet['api_key'] != "")
							{
							   $api_key=$eventsDet['api_key'];
							}
							else
							{
							   $api_key=$this->api_key;
							}
							if($eventsDet['auth_token'] != "")
							{
							   $auth_token=$eventsDet['auth_token'];
							}
							else
							{
							   $auth_token=$this->auth_token;
							}
					}
					else
					{
							$api_key=$this->api_key;
							$auth_token=$this->auth_token;
					}
				
				
					
                    
                    if($this->session->userdata('user_type')!=2)
                    {
                        if($this->input->post('print_status')==1)
                        {
                            redirect('participants/car-event-entry-form-detailed-shortcut/'.$this->input->post('participants_id').'/'.$this->input->post('events_id'));
                        }
                        else
                        {
                            if ($addEventsStatus) {
                                if($this->input->post('removeFromEvent')== 1)
                                    $this->session->set_flashdata('confirmEvents', 'Removed from Event');
                                else
                                $this->session->set_flashdata('confirmEvents', 'Events Confirmed');
                                redirect(base_url('participants'));
                            } else {
                                    if($this->input->post('removeFromEvent')== 1)
                                        $this->session->set_flashdata('confirmEventsError', 'Error in removing from events');
                                    else
                                        $this->session->set_flashdata('confirmEventsError', 'Error in confirming events');
                                    redirect(base_url('participants'));
                            }
                        }
                    }
                    else
                    {
					    $statusArray = explode('$#',$addEventsStatus);
                        if($statusArray[1]==0)
                        {
                            redirect('events/car-payment-confirm/'.$this->input->post('events_id'));
                        }
						
						
                        $dataPay['title'] = 'Proceed with Payment mode ';
                        $dataPay['payment_mode'] = $this->input->post('payment_mode');
                        $dataPay['payments_dtl']=array(
                        "purpose" => urlencode($statusArray[0]),
                        "amount" => $statusArray[1],
                        "send_email" => true, // changed to false regi after uploading changed it into true
                        "email" => $this->input->post('participants_email'),
                        'phone' => $this->input->post('phone'),
                        'buyer_name' => $this->input->post('buyer_name'),     
                        'events_id'=> $this->input->post('events_id'),     
						'api_key' => $api_key,
						'auth_token' =>  $auth_token,                  
                        "redirect_url" => base_url('events/do-after-car-payment')                       
                        );

                        // store data to flashdata
                        $this->session->set_flashdata('paymentData',$dataPay);
                        // after storing redirect it to the controller
                        redirect('instamojo/insta-mojo-create');
						//---------------
						
						
                                        
                    }
                }
        }
        
    }   



    public function categoryParticipantsList($id=NULL)
    {
        $data['title'] = 'Categorywise Participants List';
        //$data['events_details_category_id']=$id;
        $data['participants'] = $this->events_model->getAllCategoryParticipants($id);
        $this->template->write('title', 'Categorywise Participants List', TRUE);    
        
        $this->template->add_js('assets/vendors/js/tables/jquery.dataTables.min.js');
        $this->template->add_js('assets/vendors/js/tables/datatable/dataTables.bootstrap4.min.js');
        $this->template->add_js('assets/js/scripts/tables/datatables/datatable-basic.js');
        $this->template->add_css('assets/vendors/css/tables/datatable/dataTables.bootstrap4.min.css');
        $this->template->write_view('content', 'templates/pages/Events/list-category-participants', $data, TRUE);
        $this->template->render();
    }
    public function listParticipantsByEvents()
    {
        $data['title'] = 'Eventwise Participants List';
        $data['events'] = $this->events_model->getEvents();
        $this->template->write('title', 'Eventwise Participants List', TRUE);          
        $this->template->add_js('assets/vendors/js/tables/jquery.dataTables.min.js');
        $this->template->add_js('assets/vendors/js/tables/datatable/dataTables.bootstrap4.min.js');
        $this->template->add_js('assets/js/scripts/tables/datatables/datatable-basic.js');
        $this->template->add_js('assets/vendors/js/jquery.PrintArea.js');
        $this->template->add_css('assets/vendors/css/tables/datatable/dataTables.bootstrap4.min.css');
        $this->template->add_css('assets/css/style.css');
        $this->template->write_view('content', 'templates/pages/Events/list-event-participants', $data, TRUE);
        $this->template->render();
    }
	public function listParticipantsByEventsAjax()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $id = $this->input->post('event_id');
            $data['cat_id'] = $this->input->post('category');
            $data['events_id'] = $id;
            $data['events'] = $this->events_model->getSingleEvents($id);
            $data['participants'] = $this->events_model->getAllParticipantsbyEventAndCategory($id,$data['cat_id']);  
            $this->load->view('templates/pages/Events/list-event-participants-ajax', $data);
        }

    }
    public function listdashboardParticipantsByBikeEventsAjax()
    {
	    if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $id = $this->input->post('event_id');
            $data['cat_id'] = $this->input->post('category');
            $data['events_id'] = $id;
            $data['events'] = $this->events_model->getSingleEvents($id);
            $data['participants'] = $this->events_model->getAllParticipantsbyEventAndCategory($id,$data['cat_id']);  
            $this->load->view('templates/pages/Events/list-dashboard-bikeevent-participants-ajax', $data);
        }
    }
	
	
	public function listdashboardParticipantsByBikeRallyEventsAjax()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
			$id = $this->input->post('event_id');
            $data['cat_id'] = $this->input->post('category');
            $data['events_id'] = $id;

            $data['events'] = $this->events_model->getSingleBikeRallyEvents($id);
            $data['participants'] = $this->events_model->getAllParticipantsbyBikeRallyEventAndCategory($id,$data['cat_id']);    
            $this->load->view('templates/pages/Events/list-dashboard-bikerally-event-participants-ajax', $data);
        }

    }
	

	public function listdashboardParticipantsByCarEventsAjax()
    {

        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $id = $this->input->post('event_id');
            $data['cat_id'] = $this->input->post('category');
            $data['events_id'] = $id;
            $data['events'] = $this->events_model->getSingleCarEvents($id);
            $data['participants'] = $this->events_model->getAllParticipantsbyCarEventAndCategory($id,$data['cat_id']);          
            $this->load->view('templates/pages/Events/list-dashboard-car-event-participants-ajax', $data);
        }	
	}
	
	public function listdashboardParticipantsByTrainingProgramEventsAjax()
    {

        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $id = $this->input->post('event_id');
            $data['cat_id'] = $this->input->post('category');
            $data['events_id'] = $id;
            $data['events'] = $this->events_model->getTrainingProgramEvents($id);
            $data['participants'] = $this->events_model->getAllParticipantsbyTrainingProgramEventAndCategory($id,$data['cat_id']);          
            $this->load->view('templates/pages/Events/list-dashboard-training-program-event-participants-ajax', $data);
        }
    }
	
	
	/*public function listParticipantsByBikeEventsAjax()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $id = $this->input->post('event_id');
            $data['cat_id'] = $this->input->post('category');
            $data['events_id'] = $id;
            $data['events'] = $this->events_model->getSingleEvents($id);
            $data['participants'] = $this->events_model->getAllParticipantsbyEventAndCategory($id,$data['cat_id']);  
            $this->load->view('templates/pages/Events/list-bike-event-participants-ajax', $data);
        }

    }*/
    public function listParticipantsByCarEvents()
    {
     //   $data['title'] = 'Car Eventwise Participants List';
		$data['title'] = '';
        $data['events'] = $this->events_model->getCarEvents();
        $this->template->write('title', 'Car Eventwise Participants List', TRUE);          
        $this->template->add_js('assets/vendors/js/tables/jquery.dataTables.min.js');
        $this->template->add_js('assets/vendors/js/tables/datatable/dataTables.bootstrap4.min.js');
        $this->template->add_js('assets/js/scripts/tables/datatables/datatable-basic.js');
        $this->template->add_js('assets/vendors/js/jquery.PrintArea.js');
        $this->template->add_css('assets/vendors/css/tables/datatable/dataTables.bootstrap4.min.css');
        $this->template->add_css('assets/css/style.css');
        $this->template->write_view('content', 'templates/pages/Events/list-car-event-participants', $data, TRUE);
        $this->template->render();
    }
    public function listParticipantsByCarEventsAjax()
    {

        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $id = $this->input->post('event_id');
            $data['cat_id'] = $this->input->post('category');
            $data['events_id'] = $id;

            $data['events'] = $this->events_model->getSingleCarEvents($id);
            $data['participants'] = $this->events_model->getAllParticipantsbyCarEventAndCategory($id,$data['cat_id']);          
            $this->load->view('templates/pages/Events/list-car-event-participants-ajax', $data);
        }

    }

	public function listParticipantsByTrainingProgramEvents()
    {
		$data['title'] = '';
        $data['events'] = $this->events_model->getTrainingProgramEvents();
        $this->template->write('title', 'Training Program Eventwise Participants List', TRUE);          
        $this->template->add_js('assets/vendors/js/tables/jquery.dataTables.min.js');
        $this->template->add_js('assets/vendors/js/tables/datatable/dataTables.bootstrap4.min.js');
        $this->template->add_js('assets/js/scripts/tables/datatables/datatable-basic.js');
        $this->template->add_js('assets/vendors/js/jquery.PrintArea.js');
        $this->template->add_css('assets/vendors/css/tables/datatable/dataTables.bootstrap4.min.css');
        $this->template->add_css('assets/css/style.css');
        $this->template->write_view('content', 'templates/pages/Events/list-training-program-event-participants', $data, TRUE);
        $this->template->render();
    }
	
	
	
	
	
	public function listParticipantsByTrainingProgramEventsAjax()
    {

        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $id = $this->input->post('event_id');
            $data['cat_id'] = $this->input->post('category');
            $data['events_id'] = $id;
            $data['events'] = $this->events_model->getTrainingProgramEvents($id);
            $data['participants'] = $this->events_model->getAllParticipantsbyTrainingProgramEventAndCategory($id,$data['cat_id']);          
            $this->load->view('templates/pages/Events/list-training-program-event-participants-ajax', $data);
        }
    }



	

    public function categoryToGroup()
    {
        $data['title'] = 'Categorywise Participants List';
        //$data['events_details_category_id']=$id;
        $data['participants'] = $this->events_model->getAllCategoryParticipants($id);
        $this->template->write('title', 'Categorywise Participants List', TRUE);    
        
        $this->template->add_js('assets/vendors/js/tables/jquery.dataTables.min.js');
        $this->template->add_js('assets/vendors/js/tables/datatable/dataTables.bootstrap4.min.js');
        $this->template->add_js('assets/js/scripts/tables/datatables/datatable-basic.js');
        $this->template->add_css('assets/vendors/css/tables/datatable/dataTables.bootstrap4.min.css');
        $this->template->write_view('content', 'templates/pages/Events/list-category-participants', $data, TRUE);
        $this->template->render();
    }
public function categoryOfEvent()
{
     $Categories = $this->events_model->getCategoryOfEvent();
     echo'<option value="">---Select Category---</option>';
     foreach($Categories as $Category)
     {
         echo '<option value="'.$Category["categories_id"].'">'.$Category["categories_name"].'</option>';
     }
}

public function carCategoryOfEvent()
{
     $Categories = $this->events_model->getCarCategoryOfEvent();
     echo'<option value="">---Select Category---</option>';
     foreach($Categories as $Category)
     {
         echo '<option value="'.$Category["categories_id"].'">'.$Category["categories_name"].'</option>';
     }
}


public function trainingProgramCategoryOfEvent()
{
     $Categories = $this->events_model->getTrainingProgramCategoryOfEvent();
     echo'<option value="">---Select Category---</option>';
     foreach($Categories as $Category)
     {
         echo '<option value="'.$Category["categories_id"].'">'.$Category["categories_name"].'</option>';
     }
}





public function checkParticipantsCode()
{
     $participantsCodeStatus = $this->events_model->checkParticipantsCode();
    if($participantsCodeStatus)
    {
    echo '<b style="color:red">This number is already in use. </b>';
    echo '<input type="hidden" value="0" name="participantsCodeStatus" id="participantsCodeStatus" />';
    }
    else
    {
    echo '<b style="color:green"> This number is avialable.</b>';
    echo '<input type="hidden" value="1" name="participantsCodeStatus" id="participantsCodeStatus" />';
    }
}

    public function grouping()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $data['title'] = 'Event Grouping';
            $this->template->write('title', 'Event Grouping', TRUE);    
            $data['events']=$this->events_model->getEventsForGrouping();
            $this->template->add_js('assets/vendors/js/tables/jquery.dataTables.min.js');
            $this->template->add_js('assets/vendors/js/tables/datatable/dataTables.bootstrap4.min.js');
            $this->template->add_js('assets/js/scripts/tables/datatables/datatable-basic.js');
            $this->template->add_css('assets/vendors/css/tables/datatable/dataTables.bootstrap4.min.css');
            $this->template->add_css('assets/css/style.css');
            $this->template->write_view('content', 'templates/pages/Events/event-grouping', $data, TRUE);
            $this->template->render();
        }
    }
    public function groupingTableList()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $data['participants']=$this->events_model->getParticipantsForGrouping();
            $this->load->view('templates/pages/Events/grouping-table', $data);
        }
    }
    
    public function confirmGrouping()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $groupingConfirmStatus=$this->events_model->groupingConfirm();
            if ($groupingConfirmStatus) {
                $this->session->set_flashdata('gruopingConfirm', 'Grouping Process Confirmed');
                redirect('events/event-grouping');
            } else {
                $this->session->set_flashdata('gruopingConfirmError', 'Error in Grouping Process');
                redirect('events/event-grouping');
            }
        }
    }
    
    public function groupResults()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $data['title'] = 'Event Group Results';
            $this->template->write('title', 'Event Group Results', TRUE);   
            $data['events']=$this->events_model->getEventsForGrouping();
            $this->template->add_js('assets/vendors/js/tables/jquery.dataTables.min.js');
            $this->template->add_js('assets/vendors/js/tables/datatable/dataTables.bootstrap4.min.js');
            $this->template->add_js('assets/js/scripts/tables/datatables/datatable-basic.js');
            $this->template->add_js('assets/vendors/js/jquery.PrintArea.js');
            $this->template->add_css('assets/vendors/css/tables/datatable/dataTables.bootstrap4.min.css');
            $this->template->add_css('assets/css/style.css');
            $this->template->write_view('content', 'templates/pages/Events/event-group-results', $data, TRUE);
            $this->template->render();
        }
    }
    public function carGroupResults()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $data['title'] = 'Car Event Group Results';
            $this->template->write('title', 'Car Event Group Results', TRUE);   
            $data['events']=$this->events_model->getCarEventsForGrouping();
            $this->template->add_js('assets/vendors/js/tables/jquery.dataTables.min.js');
            $this->template->add_js('assets/vendors/js/tables/datatable/dataTables.bootstrap4.min.js');
            $this->template->add_js('assets/js/scripts/tables/datatables/datatable-basic.js');
            $this->template->add_js('assets/vendors/js/jquery.PrintArea.js');
            $this->template->add_css('assets/vendors/css/tables/datatable/dataTables.bootstrap4.min.css');
            $this->template->add_css('assets/css/style.css');
            $this->template->write_view('content', 'templates/pages/Events/car-event-group-results', $data, TRUE);
            $this->template->render();
        }
    }    
    
    public function groupResultsTableList()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $data['participants'] = $this->events_model->getParticipantsForResults();
            //echo '<pre>';
            //print_r($data['participants']);
            $this->load->view('templates/pages/Events/group-results-table', $data);
        }
    }

    public function carGroupResultsTableList()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $data['participants'] = $this->events_model->getCarParticipantsForResults();
            //echo '<pre>';
            //print_r($data['participants']);
            $this->load->view('templates/pages/Events/car-group-results-table', $data);
        }
    }    

    public function groupFinalResultsPublicView()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('rider'));
        }
        else
        {
            $data['title'] = 'Event Final Results';
            $this->template->write('title', 'Event Final Results', TRUE);   
            $data['events']=$this->events_model->getEventsForGrouping();
            $this->template->write_view('content', 'templates/pages/Events/event-group-final-public-results-view', $data, TRUE);
            $this->template->render();
        }
    }    
    
    public function groupFinalResultsView()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $data['title'] = 'Event Final Results';
            $this->template->write('title', 'Event Final Results', TRUE);   
            $data['events']=$this->events_model->getEventsForGrouping();
            //$this->template->add_js('assets/vendors/js/tables/jquery.dataTables.min.js');
            $this->template->add_js('assets/vendors/js/tables/datatable/dataTables.bootstrap4.min.js');
            //$this->template->add_js('assets/js/scripts/tables/datatables/datatable-basic.js');
            $this->template->add_js('assets/vendors/js/jquery.PrintArea.js');
            //$this->template->add_css('assets/vendors/css/tables/datatable/dataTables.bootstrap4.min.css');
            $this->template->add_css('assets/css/style.css');
            $this->template->write_view('content', 'templates/pages/Events/event-group-final-results-view', $data, TRUE);
            $this->template->render();
        }
    }
    public function groupFinalResultsTableList()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $event=$this->input->post('events_name');
            $data['events'] = $this->events_model->getSingleEvents($event);
            $data['participants']=$this->events_model->getParticipantsForFinalResultsView();
            //echo '<pre>';
            //print_r($data['participants']);
            $this->load->view('templates/pages/Events/group-final-results-table', $data);
        }
    }

    public function publicFinalResultsView($e_id=NULL)
    {
        if($e_id==NULL)
        {   
            $ajaxStatus=$this->input->post('ajaxStatus');
            $data['title'] = 'Final Results';
            $data['events']=$this->events_model->getEventsForGrouping();
            if($ajaxStatus==1)
                $this->load->view('templates/pages/Events/event-public-final-results-search-view', $data);
            else
            $this->load->view('templates/pages/Events/event-public-final-results-view', $data);
            
        }
        else
        {
            $organiser=$this->input->post('organiser');
            if($organiser!=NULL)
            $data['title'] = 'Final Results Organised By '.$organiser;
            else
            $data['title'] = 'Final Results';
            //$e_id=base64_decode($e_id);
            $data['events'] = $this->events_model->getSingleEvents($e_id);
            $data['participants']=$this->events_model->getParticipantsForFinalPublicResultsView($e_id);
            $this->load->view('templates/pages/Events/public-final-results-table', $data);
        }
    }
    public function publicFinalResultsTableList()
    {
            $data['participants']=$this->events_model->getParticipantsForFinalResultsView();
            $this->load->view('templates/pages/Events/public-final-results-table', $data);
    }
    
    
    public function confirmGroupResults()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $groupResultsConfirmStatus=$this->events_model->groupResultConfirm();
            if ($groupResultsConfirmStatus) {
                $this->session->set_flashdata('gruopResultsConfirm', 'Group Results Process Confirmed');
                redirect('events/event-group-results');
            } else {
                $this->session->set_flashdata('gruopResultsConfirmError', 'Error in Group Results Process');
                redirect('events/event-group-results');
            }
        }
    }
    public function updateReportLink()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $updateReportLinkStatus=$this->events_model->updateReportLink();
            if ($updateReportLinkStatus) {
                $this->session->set_flashdata('updateReportLink', 'Report link is updated successfully');
                redirect('events/event-group-final-results-view');
            } else {
                $this->session->set_flashdata('updateReportLinkError', 'Error in report link update process');
                redirect('events/event-group-final-results-view');
            }
        }
    }
    public function viewEventsTransaction($id)
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $data['title'] = 'View Events Transaction';
            $this->template->write('title', 'View Events Transaction', TRUE);   
            $data['categories'] = $this->events_model->getCategoryOfEvent($id);
            $data['transaction']= $this->events_model->getPaymentOfEvent($id,$cat_id=NULL);
            $this->template->add_js('assets/vendors/js/tables/jquery.dataTables.min.js');
            $this->template->add_js('assets/vendors/js/tables/datatable/dataTables.bootstrap4.min.js');
            $this->template->add_js('assets/js/scripts/tables/datatables/datatable-basic.js');
            $this->template->add_css('assets/vendors/css/tables/datatable/dataTables.bootstrap4.min.css');
            $this->template->add_css('assets/css/style.css');
            $this->template->write_view('content', 'templates/pages/Events/view-events-collection', $data, TRUE);
            $this->template->render();
        }
    }
    public function viewEventsTransactionAjax()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $cat_id=$this->input->post('cat_id');
            $data['transaction']= $this->events_model->getPaymentOfEvent($id=NULL,$cat_id);
            $this->load->view('templates/pages/Events/view-events-collection-ajax', $data);
        }
        
        
    }

    public function viewUserPayments()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $data['title'] = 'View Payment Transaction';
            $this->template->write('title', 'View Payment Transaction', TRUE);   
            $data['categories'] = array();//$this->events_model->getCategoryOfEvent($id);
            $data['transactions']= $this->events_model->getPaymentTransactions();
			$this->template->add_css('assets/css/style-admin.css');
			$this->template->add_css('assets/css/menu.css');
			$this->template->add_css('assets/css/menu-admin.css');
			$this->template->add_css('assets/css/jquery-ui.css');
			$this->template->add_css('assets/css/glyphicon.css');
			$this->template->add_css('assets/css/float-label.css');
            $this->template->write_view('content', 'templates/pages/Events/view-payment-transaction', $data, TRUE);
            $this->template->render();
        }
    }

    public function confirmUPIPaymentDashboard()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $data['title'] = 'Confirm UPI Payment';
            $this->template->write('title', 'View Payment Transaction', TRUE);   
        //  $data['categories'] = array();//$this->events_model->getCategoryOfEvent($id);
		//	$data['categories'] = $this->events_model->getCategoryOfEvent($id);
            $data['transactions']= $this->events_model->getUPIPaymentTransactions();
			$this->template->add_css('assets/css/style-admin.css');
			$this->template->add_css('assets/css/menu.css');
			$this->template->add_css('assets/css/menu-admin.css');
			$this->template->add_css('assets/css/jquery-ui.css');
			$this->template->add_css('assets/css/glyphicon.css');
			$this->template->add_css('assets/css/float-label.css');
            $this->template->write_view('content', 'templates/pages/Events/confirm-upi-payment-transaction-dashboard', $data, TRUE);
            $this->template->render();
        }
    }
    public function confirmCarUPIPaymentDashboard()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $data['title'] = 'Confirm Car UPI Payment';
            $this->template->write('title', 'View Payment Transaction', TRUE);   
            $data['categories'] = array();//$this->events_model->getCategoryOfEvent($id);
            $data['transactions']= $this->events_model->getCarUPIPaymentTransactions();
			$this->template->add_css('assets/css/style-admin.css');
			$this->template->add_css('assets/css/menu.css');
			$this->template->add_css('assets/css/menu-admin.css');
			$this->template->add_css('assets/css/jquery-ui.css');
			$this->template->add_css('assets/css/glyphicon.css');
			$this->template->add_css('assets/css/float-label.css');
            $this->template->write_view('content', 'templates/pages/Events/confirm-car-upi-payment-transaction-dashboard', $data, TRUE);
            $this->template->render();
        }
    }
    public function confirmUPIPaymentProcess()
    {

        echo $status = $this->events_model->confirmUPIPaymentProcess();
    }
    public function confirmCarUPIPaymentProcess()
    {

        echo $status = $this->events_model->confirmCarUPIPaymentProcess();
    }
    
	public function confirmTrainingProgramUPIPaymentProcess()
    {

        echo $status = $this->events_model->confirmTrainingProgramUPIPaymentProcess();
    }
    
	
	
	
    public function deleteEvents($id)
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $deleteEventsStatus = $this->events_model->deleteEvents($id);
            if ($deleteEventsStatus) {
                $this->session->set_flashdata('deleteEvents', 'Events Deleted');
                    redirect(base_url('events'));
            } else {
                $this->session->set_flashdata('deleteEventsError', 'Error in Deleting events');
                    redirect(base_url('events'));
            }
        }
    }


    public function deleteCarEvents($id)
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $deleteEventsStatus = $this->events_model->deleteCarEvents($id);
            if ($deleteEventsStatus) {
                $this->session->set_flashdata('deleteEvents', 'Events Deleted');
                    redirect(base_url('events/car-events'));
            } else {
                $this->session->set_flashdata('deleteEventsError', 'Error in Deleting events');
                    redirect(base_url('events/car-events'));
            }
        }
    }
    public function doAfterPayment_()
    {
        //print_r($response);exit;
        $data = Array();
        $this->template->write_view('content', 'templates/pages/Events/loader-spinner', $data, TRUE);
        $this->template->render();
    }
	
	public function test()
	{
	}
	
    public function doAfterPayment()
    {
		 //  added on 20th nov 2022 
		 if(isset($_GET["events_id"]))
		 {
				$e_id=$_GET["events_id"];
				$eventsDet = $this->events_model->getSingleEvents($_GET['events_id']);
				if($eventsDet)
				{
						if($eventsDet['api_key'] != "")
						{
						   $api_key=$eventsDet['api_key'];
						}
						else
						{
						   $api_key=$this->api_key;
						}
						if($eventsDet['auth_token'] != "")
						{
						   $auth_token=$eventsDet['auth_token'];
						}
						else
						{
						   $auth_token=$this->auth_token;
						}
				}
				else
				{
						$api_key=$this->api_key;
						$auth_token=$this->auth_token;
				}
		 }
		 //  added on 20th nov 2022 
		 /*echo "<pre>";
		 print_r($eventsDet);*/


       if(isset($_GET['payment_id'])!='' && isset($_GET['payment_request_id'])!='')
        {
			$ch = curl_init();
            $url = $this->endpoint.$_GET['payment_request_id'];
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HEADER, FALSE);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, TRUE);
            curl_setopt($ch, CURLOPT_HTTPHEADER,
                        array("X-Api-Key:".$api_key,
                              "X-Auth-Token:".$auth_token));
            $response = curl_exec($ch);
            curl_close($ch);

			$result = json_decode($response);		//$response$this->paymentRequestStatus($_GET['payment_request_id']);
			sleep(2);
			$data['payment_requests']['success']=$result->payment_request->payments[0]->status;
			$data['payment_requests']['mode']=$_GET['mode'];
			$data['payment_requests']['trans_id']=$result->payment_request->id;
			$data['payment_requests']['payment_id']=$result->payment_request->payments[0]->payment_id;
			$data['payment_requests']['purpose']=urldecode($result->payment_request->purpose);
			$data['payment_requests']['events_id']=$_GET['events_id'];
			$data['payment_requests']['user_id']=$this->session->userdata('user_id');
			//print_r($data['payment_requests']);exit;
        }
        else if(isset($_GET['mode'])!='' && isset($_GET['purpose'])!='')
        {
			$data['payment_requests']['success']='Pending';
			$data['payment_requests']['mode']=$_GET['mode'];
			$data['payment_requests']['trans_id']='-';
			$data['payment_requests']['payment_id']='-';
			$data['payment_requests']['purpose']=urldecode($_GET['purpose']);
			$data['payment_requests']['events_id']=$_GET['events_id'];
			$data['payment_requests']['user_id']=$this->session->userdata('user_id');
        }
        else
        {
            show_404();

        }
      	// print_r($data['payment_requests']);exit;
        	$data['paymentConfirm']=$this->adds_model->addsList(28);
            $payment_update_dtls=$this->events_model->paymentUpdate($data['payment_requests']);
			
            $data['title'] = 'Payment '.$data['payment_requests']['success'].'!';
          //  $e_id=$payment_update_dtls[0]['payment_event_id'];  // commented nov 20th 2022
            $data['events_id']=$e_id;
			$data['participants_events_id']=$e_id;
            $data['participants_id']=$this->session->userdata('user_id');
            $data['participants'] = $this->events_model->getSingleParticipants($this->session->userdata('user_id'));
            $data['events'] = $this->events_model->getSingleEvents($e_id);
			$eventsOrganizer=$data['events'];
			$eventsOrganizerId=$eventsOrganizer['events_organizer'];
			$organizerBikeEventDetails=$this->events_model->getAllEventsByOrganizer($eventsOrganizerId);
			$organizerBikeRallyEventDetails=$this->events_model->getAllBikeRallyEventsByOrganizer($eventsOrganizerId);
			$organizerCarEventDetails=$this->events_model->getAllCarEventsByOrganizer($eventsOrganizerId);
			$data['organizerEventDetails']=array_merge($organizerBikeEventDetails,$organizerBikeRallyEventDetails,$organizerCarEventDetails);
			$dates = array();
			foreach ($data['organizerEventDetails'] as $key => $row) {
			  $dates[$key] = strtotime($row['events_date']);
			}
			// Sort $data based on dates in descending order
			array_multisort($dates, SORT_DESC, $data['organizerEventDetails']);
            $data['payment_status'] = $data['payment_requests']['success'];
            $data['eventParticipants'] = $this->events_model->getAllParticipantsbyEventSuccessPayment($e_id);
            //if($data['events']==NULL)
            //$data['events']['categories']= array();
            $data['participants_categories'] = $this->events_model->getParticipantsCategories($this->session->userdata('user_id'),$e_id);

            $data['confirm_status'] = 1;
			$data['e_id'] = $e_id;
            $data['payments'] = $this->events_model->getPaymantsdetails($this->session->userdata('user_id'),$e_id);
            $data['event_status'] = $data['participants_categories']['events_details']['status']; 
            
               // $data['payment_requests']['success'] = 'Success';
            $this->template->write('title', 'Payment '.$data['payment_requests']['success'].'!', TRUE);  
            $this->session->set_flashdata('paymentStatus', $data['payment_requests']['success']);
            
            $this->template->add_css('assets/css/style2.css');
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');

            //$this->template->add_js('assets/js/bootstrap-select.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');   
            $data['eventPaymentRegInfo'] = $this->events_model->getEventPaymentRegInfo($e_id,$this->session->userdata('user_id'));
			$data['eventType']="Bike";
            if(isset($_GET['mode'])!='' && isset($_GET['purpose'])!='')
			{
                $data['event_entry_details'] = $this->events_model->getEventEntryDetails($data['participants_id'],$e_id);
                //print_r($data['event_entry_details']);
                if($data['event_entry_details'][0]['upi_details_transaction_id'] == '')
				{
					$data['advtDetails']=$this->adds_model->addsList(35);
				    $this->template->write_view('content', 'templates/pages/Events/upi-payment', $data, TRUE);
			    }
                else
				{
                    $this->template->write_view('content', 'templates/pages/Events/payment-process', $data, TRUE);
				}
            }
            else 
                $this->template->write_view('content', 'templates/pages/Events/payment-confirm', $data, TRUE);
            $this->template->render();            

    }

    public function doAfterCarPayment()
    {
        //print_r($response);exit;
        
        //if($_GET['payment_request_id']!='')
		
		$eventsDet = $this->events_model->getSingleCarEvents($_GET['events_id']);
		$data['paymentConfirm']=$this->adds_model->addsList(30);
		if($eventsDet)
		{
				if($eventsDet['api_key'] != "")
				{
				   $api_key=$eventsDet['api_key'];
				}
				else
				{
				   $api_key=$this->api_key;
				}
				if($eventsDet['auth_token'] != "")
				{
				   $auth_token=$eventsDet['auth_token'];
				}
				else
				{
				   $auth_token=$this->auth_token;
				}
		}
		else
		{
				$api_key=$this->api_key;
				$auth_token=$this->auth_token;
		}
		
        if(isset($_GET['payment_id'])!='' && isset($_GET['payment_request_id'])!='')
        {
        

            $ch = curl_init();
            $url = $this->endpoint.$_GET['payment_request_id'];
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HEADER, FALSE);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, TRUE);
            curl_setopt($ch, CURLOPT_HTTPHEADER,
                        array("X-Api-Key:".$api_key,
                              "X-Auth-Token:".$auth_token));
            $response = curl_exec($ch);
            curl_close($ch);

        $result = json_decode($response);//$response$this->paymentRequestStatus($_GET['payment_request_id']);
        sleep(2);
        //print_r($result);
        // "Payment Successully Done!";
        //$data['payment_requests']=$_GET['payment_requests'];
            if($_GET['payment_status']!='Failed'){
            $data['payment_requests']['success']=$result->payment_request->payments[0]->status;
            $data['payment_requests']['mode']='online';
            $data['payment_requests']['trans_id']=$result->payment_request->id;
            $data['payment_requests']['payment_id']=$result->payment_request->payments[0]->payment_id;
            $data['payment_requests']['purpose']=urldecode($result->payment_request->purpose);
            }
            else{
                $data['payment_requests']['success']='Pending';
                $data['payment_requests']['mode']='online';
                $data['payment_requests']['trans_id']=$_GET['payment_request_id'];
                $data['payment_requests']['payment_id']=$_GET['payment_id'];
                $data['payment_requests']['purpose']=urldecode($result->payment_request->purpose);
                }
        }   
        else if(isset($_GET['mode'])!='' && isset($_GET['purpose'])!='')
        {

        $data['payment_requests']['success']='Pending';
        $data['payment_requests']['mode']=$_GET['mode'];
        $data['payment_requests']['trans_id']='-';
        $data['payment_requests']['payment_id']='-';
        $data['payment_requests']['purpose']=urldecode($_GET['purpose']);
        $data['payment_requests']['events_id']=$_GET['events_id'];
        $data['payment_requests']['user_id']=$this->session->userdata('user_id');
        }
        else
        {
            show_404();

        }
            $payment_update_dtls=$this->events_model->carPaymentUpdate($data['payment_requests']);
            $data['title'] = 'Payment '.$data['payment_requests']['success'].'!';
            $e_id=$payment_update_dtls[0]['payment_event_id'];
            $data['events_id']=$e_id;
            $data['participants_id']=$this->session->userdata('user_id');
            $data['participants'] = $this->events_model->getSingleParticipants($this->session->userdata('user_id'));
            $data['events'] = $this->events_model->getSingleCarEvents($e_id);
			$eventsOrganizer=$data['events'];
			$eventsOrganizerId=$eventsOrganizer['events_organizer'];
			$organizerBikeEventDetails=$this->events_model->getAllEventsByOrganizer($eventsOrganizerId);
			$organizerBikeRallyEventDetails=$this->events_model->getAllBikeRallyEventsByOrganizer($eventsOrganizerId);
			$organizerCarEventDetails=$this->events_model->getAllCarEventsByOrganizer($eventsOrganizerId);
			$data['organizerEventDetails']=array_merge($organizerBikeEventDetails,$organizerBikeRallyEventDetails,$organizerCarEventDetails);
			$dates = array();
			foreach ($data['organizerEventDetails'] as $key => $row) {
			  $dates[$key] = strtotime($row['events_date']);
			}
			// Sort $data based on dates in descending order
			array_multisort($dates, SORT_DESC, $data['organizerEventDetails']);
			$p_id = $this->session->userdata('user_id');
		    $data['eventPaymentRegInfo'] = $this->events_model->getCarEventPaymentRegInfo($e_id,$p_id);
			
            $data['payment_status'] = $data['payment_requests']['success'];
            $data['eventParticipants'] = $this->events_model->getAllParticipantsbyCarEventSuccessPayment($e_id);
 			// $data['checkPaymentWithUPI'] = $this->events_model->checkPaymentWithUPI($e_id,$p_id);
			
			////////////////////////////////////////////////////////////////////
			$data['e_id'] = $e_id;
        //    $p_id = $this->session->userdata('user_id');
            $data['checkPaymentWithUPI'] = $this->events_model->checkPaymentWithUPI($e_id,$p_id);
			/////////////////////////////////////////////////////////////////////
 
            // if($data['events']==NULL)
            // $data['events']['categories']= array();
            $data['participants_categories'] = $this->events_model->getCarParticipantsCategories($this->session->userdata('user_id'),$e_id);

            $data['confirm_status'] = 1;
            $data['payments'] = $this->events_model->getCarPaymantsdetails($this->session->userdata('user_id'),$e_id);
            $data['event_status'] = $data['participants_categories']['events_details']['status'];
            $this->template->write('title', 'Payment '.$data['payment_requests']['success'].'!', TRUE);  
            $data['eventPaymentRegInfo'] = $this->events_model->getCarEventPaymentRegInfo($e_id,$this->session->userdata('user_id'));

            $this->template->add_css('assets/css/style2.css');
            $this->template->add_css('assets/css/select2.min.css');
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');

            //$this->template->add_js('assets/js/bootstrap-select.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');   
            $this->template->add_js('assets/js/select2.min.js');
			
			$data['eventType']="Car";
            if(isset($_GET['mode'])!='' && isset($_GET['purpose'])!=''){
                $data['event_entry_details'] = $this->events_model->getCarEventEntryDetails($data['participants_id'],$e_id);
                //print_r($data['event_entry_details']);
                if($data['event_entry_details'][0]['upi_details_transaction_id'] == '')
				{
				    $data['advtDetails']=$this->adds_model->addsList(32);
                    $this->template->write_view('content', 'templates/pages/Events/car-upi-payment', $data, TRUE);
				}
                else
				{
					$data['carPaymentProcess']=$this->adds_model->addsList(27);
                    $this->template->write_view('content', 'templates/pages/Events/car-payment-process', $data, TRUE);
				}
            }
            else
				{ 
					$data['advtDetails']=$this->adds_model->addsList(34);
              		$this->template->write_view('content', 'templates/pages/Events/payment-confirm', $data, TRUE);
				}
            $this->template->render();            

    }    
    public function removeParticipantsFromEvents($p_id,$e_id)
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $removeEventsStatus = $this->events_model->removeParticipantsFromEvents($p_id,$e_id);
            if ($removeEventsStatus) {
                $this->session->set_flashdata('removeFromEvents', 'Participats removed from events');
                    redirect(base_url('events'));
            } else {
                $this->session->set_flashdata('removeFromEventsError', 'Error in participats removing from events');
                    redirect(base_url('events'));
            }            
        }
    }
	
	
	public function addToBikeRallyEvents($id)
    {
	
	    if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
		
	
			$data['title'] = 'Add To Bike Rally Events';
            $data['events_id']=$id;
           
		  
		    if($this->session->userdata('user_type')==2) 
			{
              $this->confirmBikeRallyParticipants($this->session->userdata('user_id'),$id); 
            } 
			else
            {
			
			
            $data['participants'] = $this->events_model->getAllParticipants();
            $data['events'] = $this->events_model->getSingleBikeRallyEvents($id); 
			
			
			
            $this->template->write('title', 'Add To Bike Rally Events', TRUE); 
            $this->template->add_js('assets/vendors/js/tables/jquery.dataTables.min.js');
            $this->template->add_js('assets/vendors/js/tables/datatable/dataTables.bootstrap4.min.js');
            $this->template->add_js('assets/js/scripts/tables/datatables/datatable-basic.js');
            $this->template->add_css('assets/vendors/css/tables/datatable/dataTables.bootstrap4.min.css');
            $this->template->write_view('content', 'templates/pages/Events/bikerally-list-participants', $data, TRUE);
            $this->template->render();
            }
        }
    }  
	
	
	public function confirmBikeRallyParticipants($p_id,$e_id)
    { 
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
			$data['bikeRallyEventParticipantsSidebarList']=$this->adds_model->addsList(13);
			$data['bikeRallyEventOrganizerSidebarList']=$this->adds_model->addsList(14);
            $data['title'] = 'Confirm Bike Rally Participants';
            $data['events_id']=$e_id;
            $data['participants_id']=$p_id;
            $participantsCode = array();
            $data['participants'] = $this->events_model->getSingleParticipants($p_id);
			$data['eventType'] = "Bike Rally";
			$data['eventParticipants'] = $this->events_model->getAllParticipantsbyBikeRallyEventSuccessPayment($e_id);
			$data['eventPendingParticipants'] = $this->events_model->getAllParticipantsbyBikeRallyEventPendingPayment($e_id);
			
			
			
            $data['orderedCategoryLists'] = $this->events_model->getTypeOrderedBikeRallyCategoryList($e_id);// echo '<pre>';print_r($data['orderedCategoryLists']);exit;
            $participantsCode=$this->events_model->getAllBikeRallyParticipantsCode($e_id);
            if($this->input->post('removeFromEvent') == 1)
            {
                $this->removeParticipantsFromEvents($p_id,$e_id);
            }
            for($i=1;$i<=100;$i++)
            {
                $a[$i-1]=$i;
            }
            if($participantsCode == NULL )
            {
                $participantsCode['participants_code']= array();           
            }
			
			$data['participants_code'] = $participantsCode;
			 
            $data['participants_bill']=$this->events_model->getBikeRallyParticipantsBill($p_id,$e_id);
            $data['events'] = $this->events_model->getSingleBikeRallyEvents($e_id);
			
			$eventsOrganizer=$data['events'];
			$eventsOrganizerId=$eventsOrganizer['events_organizer'];
			$organizerBikeEventDetails=$this->events_model->getAllEventsByOrganizer($eventsOrganizerId);
			$organizerBikeRallyEventDetails=$this->events_model->getAllBikeRallyEventsByOrganizer($eventsOrganizerId);
			$organizerCarEventDetails=$this->events_model->getAllCarEventsByOrganizer($eventsOrganizerId);
			$data['organizerEventDetails']=array_merge($organizerBikeEventDetails,$organizerBikeRallyEventDetails,$organizerCarEventDetails);
			$dates = array();
			foreach ($data['organizerEventDetails'] as $key => $row) {
				$dates[$key] = strtotime($row['events_date']);
			}
			// Sort $data based on dates in descending order
			array_multisort($dates, SORT_DESC, $data['organizerEventDetails']);
			
			
			
			
			$data['orderedCategoryLists'] = $this->events_model->getTypeOrderedBikeRallyCategoryList($e_id);
            if($data['events']==NULL)
            $data['events']['categories']= array();
            $data['participants_categories'] = [];
            // check for already registered to this event. 
            $data['event_status'] = 0;
            if(empty($data['participants_categories']))
                $data['confirm_status'] = 0;
            else
            {
                $data['confirm_status'] = 1;
                $data['payments'] = $this->events_model->getBikeRallyPaymantsdetails($p_id,$e_id);
                $data['event_status'] = $data['participants_categories']['events_details']['status'];
            }
            $this->template->write('title', 'Confirm Participants', TRUE);  
			$this->template->add_css('assets/css/glyphicon.css');
            $this->template->add_css('assets/css/seat.css');
            $this->template->add_css('assets/css/owl.carousel.css');
            $this->template->add_css('assets/css/owl.theme.default.css');
            $this->template->add_css('assets/css/style2.css');
            $this->template->add_css('assets/css/select2.min.css');        
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
            $this->template->add_js('assets/js/vendors/owl.carousel.js');
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');
            $this->template->add_js('assets/js/participant-list.js');
            $this->template->add_js('assets/js/select2.min.js');
            $this->template->write_view('content', 'templates/pages/Events/confirm-bikerally-participants', $data, TRUE);
            $this->template->render();
        }
    }
	

	
	public function doAfterBikeRallyPayment()
    {
		$e_id=$_GET["events_id"];
        //if($_GET['payment_request_id']!='')
		
		$data['bikerallyPaymentProcess']=$this->adds_model->addsList(26);
		$eventsDet = $this->events_model->getSingleBikeRallyEvents($_GET['events_id']);
		if($eventsDet)
		{
				if($eventsDet['api_key'] != "")
				{
				   $api_key=$eventsDet['api_key'];
				}
				else
				{
				   $api_key=$this->api_key;
				}
				if($eventsDet['auth_token'] != "")
				{
				   $auth_token=$eventsDet['auth_token'];
				}
				else
				{
				   $auth_token=$this->auth_token;
				}
		}
		else
		{
				$api_key=$this->api_key;
				$auth_token=$this->auth_token;
		}
		
	//	echo "<pre>";
		
	//	print_r($eventsDet);
		
		
		
        if(isset($_GET['payment_id'])!='' && isset($_GET['payment_request_id'])!='')
        {
		//	echo "payment id = " . $_GET['payment_id'] . "<br>";
		//	echo "payment request_id  = " . $_GET['payment_request_id'] . "<br>";
		
            $ch = curl_init();
            $url = $this->endpoint.$_GET['payment_request_id'];
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HEADER, FALSE);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, TRUE);
            curl_setopt($ch, CURLOPT_HTTPHEADER,
                        array("X-Api-Key:".$api_key,
                              "X-Auth-Token:".$auth_token));
            $response = curl_exec($ch);
            curl_close($ch);

			$result = json_decode($response);//$response$this->paymentRequestStatus($_GET['payment_request_id']);
			sleep(2);
			//print_r($result);
			// "Payment Successully Done!";
			//$data['payment_requests']=$_GET['payment_requests'];
          //  if($_GET['payment_status']!='Failed'){
            $data['payment_requests']['success']=$result->payment_request->payments[0]->status;
            $data['payment_requests']['mode']=$_GET['mode'];
            $data['payment_requests']['trans_id']=$result->payment_request->id;
            $data['payment_requests']['payment_id']=$result->payment_request->payments[0]->payment_id;
            $data['payment_requests']['purpose']=urldecode($result->payment_request->purpose);
			$data['payment_requests']['events_id']=$_GET['events_id'];
			$data['payment_requests']['user_id']=$this->session->userdata('user_id');
         //   }
           /* else{
                $data['payment_requests']['success']='Pending';
                $data['payment_requests']['mode']='online';
                $data['payment_requests']['trans_id']=$_GET['payment_request_id'];
                $data['payment_requests']['payment_id']=$_GET['payment_id'];
                $data['payment_requests']['purpose']=urldecode($result->payment_request->purpose);
                }*/
        }   
        else if(isset($_GET['mode'])!='' && isset($_GET['purpose'])!='')
        {
		//	echo "in else if ";
			$data['payment_requests']['success']='Pending';
			$data['payment_requests']['mode']=$_GET['mode'];
			$data['payment_requests']['trans_id']='-';
			$data['payment_requests']['payment_id']='-';
			$data['payment_requests']['purpose']=urldecode($_GET['purpose']);
			$data['payment_requests']['events_id']=$_GET['events_id'];
			$data['payment_requests']['user_id']=$this->session->userdata('user_id');
        }
        else
        {
            show_404();
        }
		//  echo "after";
            $payment_update_dtls=$this->events_model->bikerallyPaymentUpdate($data['payment_requests']);
		//	print_r($payment_update_dtls);
			
            $data['title'] = 'Payment '.$data['payment_requests']['success'].'!';
         //   $e_id=$payment_update_dtls[0]['payment_event_id'];
            $data['events_id']=$e_id;
            $data['participants_id']=$this->session->userdata('user_id');
            $data['participants'] = $this->events_model->getSingleParticipants($this->session->userdata('user_id'));
			
		//	echo "inn";
			
            $data['events'] = $this->events_model->getSingleBikeRallyEvents($e_id);
			
			
			$eventsOrganizer=$data['events'];
			$eventsOrganizerId=$eventsOrganizer['events_organizer'];
			$organizerBikeEventDetails=$this->events_model->getAllEventsByOrganizer($eventsOrganizerId);
			$organizerBikeRallyEventDetails=$this->events_model->getAllBikeRallyEventsByOrganizer($eventsOrganizerId);
			$organizerCarEventDetails=$this->events_model->getAllCarEventsByOrganizer($eventsOrganizerId);
			$data['organizerEventDetails']=array_merge($organizerBikeEventDetails,$organizerBikeRallyEventDetails,$organizerCarEventDetails);
			$dates = array();
			foreach ($data['organizerEventDetails'] as $key => $row) {
			  $dates[$key] = strtotime($row['events_date']);
			}
			// Sort $data based on dates in descending order
			array_multisort($dates, SORT_DESC, $data['organizerEventDetails']);
			
			
			
		//	print_r($data['events']);
			
			$p_id = $this->session->userdata('user_id');
		    $data['eventPaymentRegInfo'] = $this->events_model->getBikeRallyEventPaymentRegInfo($e_id,$p_id);
			
		//	print_r($data['eventPaymentRegInfo']);
			
            $data['payment_status'] = $data['payment_requests']['success'];
            $data['eventParticipants'] = $this->events_model->getAllParticipantsbyBikeRallyEventSuccessPayment($e_id);
			$data['eventPendingParticipants'] = $this->events_model->getAllParticipantsbyBikeRallyEventPendingPayment($e_id);
			
			
			
		//	print_r($data['eventParticipants']);
 			// $data['checkPaymentWithUPI'] = $this->events_model->checkPaymentWithUPI($e_id,$p_id);
			
			////////////////////////////////////////////////////////////////////
			$data['e_id'] = $e_id;
        //    $p_id = $this->session->userdata('user_id');
            $data['checkPaymentWithUPI'] = $this->events_model->checkPaymentWithUPI($e_id,$p_id);        //////  doubt, this model uses payment table
			/////////////////////////////////////////////////////////////////////
 
            // if($data['events']==NULL)
            // $data['events']['categories']= array();
            $data['participants_categories'] = $this->events_model->getBikeRallyParticipantsCategories($this->session->userdata('user_id'),$e_id);

            $data['confirm_status'] = 1;
            $data['payments'] = $this->events_model->getCarPaymantsdetails($this->session->userdata('user_id'),$e_id);
            $data['event_status'] = $data['participants_categories']['events_details']['status'];
            $this->template->write('title', 'Payment '.$data['payment_requests']['success'].'!', TRUE);  
            $data['eventPaymentRegInfo'] = $this->events_model->getBikeRallyEventPaymentRegInfo($e_id,$this->session->userdata('user_id'));
			
			

            $this->template->add_css('assets/css/style2.css');
            $this->template->add_css('assets/css/select2.min.css');
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');

            //$this->template->add_js('assets/js/bootstrap-select.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');   
            $this->template->add_js('assets/js/select2.min.js');
			
			$data['eventType']="Bike Rally";
			/*echo "<pre>";
			print_r($data);*/
			
            if(isset($_GET['mode'])!='' && isset($_GET['purpose'])!='')
			{
                $data['event_entry_details'] = $this->events_model->getBikeRallyEventEntryDetails($data['participants_id'],$e_id);
                if($data['event_entry_details'][0]['upi_details_transaction_id'] == '')
				{
				  $data['advtDetails']=$this->adds_model->addsList(36);
                  $this->template->write_view('content', 'templates/pages/Events/bikerally-upi-payment', $data, TRUE);
				}
                else
				{
				
                    $this->template->write_view('content', 'templates/pages/Events/bikerally-payment-process', $data, TRUE);
				}
            }
            else 
			{
				   $data['paymentConfirm']=$this->adds_model->addsList(29);
                   $this->template->write_view('content', 'templates/pages/Events/payment-confirm', $data, TRUE);
			}
            $this->template->render();            

    } 
	
	public function addParticipantsToBikeRallyEvents()
    {
	
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
		
            $this->form_validation->set_rules('events_id', 'Event Id', 'required');
            $this->form_validation->set_rules('participants_id', 'Participants Id', 'required');
            $this->form_validation->set_rules('category_cnt', 'Category', 'required');
			
            if ($this->form_validation->run() == false)
			{
                $this->session->set_flashdata('confirmEventsError', 'Error in confirming events');
                redirect('events/bikerally-events');
            }
            else 
			{
                    if($this->input->post('removeFromEvent')== 1)
					{
                        $addEventsStatus = $this->events_model->removeCarParticipantsFromEvents($this->input->post('participants_id'),$this->input->post('events_id'));
					}
                    else
					{
						$addEventsStatus = $this->events_model->addParticipantsToBikeRallyEvents();
					}	
				//		exit;
					
                    $data['events'] = $this->events_model->getSingleBikeRallyEvents($this->input->post('events_id'));
					
					$eventsDet = $this->events_model->getSingleBikeRallyEvents($this->input->post('events_id'));
					
					
					if($eventsDet)
					{
							if($eventsDet['api_key'] != "")
							{
							   $api_key=$eventsDet['api_key'];
							}
							else
							{
							   $api_key=$this->api_key;
							}
							if($eventsDet['auth_token'] != "")
							{
							   $auth_token=$eventsDet['auth_token'];
							}
							else
							{
							   $auth_token=$this->auth_token;
							}
					}
					else
					{
							$api_key=$this->api_key;
							$auth_token=$this->auth_token;
					}
					
					
                    if($this->session->userdata('user_type')!=2)
                    {
                        if($this->input->post('print_status')==1)
                        {
                            redirect('participants/bikerally-event-entry-form-detailed-shortcut/'.$this->input->post('participants_id').'/'.$this->input->post('events_id'));
                        }
                        else
                        {
						    if ($addEventsStatus) 
							{
                                if($this->input->post('removeFromEvent')== 1)
                                    $this->session->set_flashdata('confirmEvents', 'Removed from Event');
                                else
                                $this->session->set_flashdata('confirmEvents', 'Events Confirmed');
                                redirect(base_url('participants'));
                            } else {
                                    if($this->input->post('removeFromEvent')== 1)
                                        $this->session->set_flashdata('confirmEventsError', 'Error in removing from events');
                                    else
                                        $this->session->set_flashdata('confirmEventsError', 'Error in confirming events');
                                    redirect(base_url('participants'));
                            }
                        }
                    }
                    else
                    {
					
						$eventsOrganizer=$data['events'];
						$eventsOrganizerId=$eventsOrganizer['events_organizer'];
						$organizerBikeEventDetails=$this->events_model->getAllEventsByOrganizer($eventsOrganizerId);
						$organizerBikeRallyEventDetails=$this->events_model->getAllBikeRallyEventsByOrganizer($eventsOrganizerId);
						$organizerCarEventDetails=$this->events_model->getAllCarEventsByOrganizer($eventsOrganizerId);
						$data['organizerEventDetails']=array_merge($organizerBikeEventDetails,$organizerBikeRallyEventDetails,$organizerCarEventDetails);
						$dates = array();
						foreach ($data['organizerEventDetails'] as $key => $row) {
						  $dates[$key] = strtotime($row['events_date']);
						}
						// Sort $data based on dates in descending order
						array_multisort($dates, SORT_DESC, $data['organizerEventDetails']);
                        $statusArray = explode('$#',$addEventsStatus);
                        if($statusArray[1]==0)
                        {
                            redirect('events/bikerally-payment-confirm/'.$this->input->post('events_id'));
                        }
						
                        $dataPay['title'] = 'Proceed with Payment mode ';
                        $dataPay['payment_mode'] = $this->input->post('payment_mode');
                        $dataPay['payments_dtl']=array(
                        "purpose" => urlencode($statusArray[0]),
                        "amount" => $statusArray[1],
                        "send_email" => true,   // changed to false regi after uploading changed it into true
                        "email" => $this->input->post('participants_email'),
                        'phone' => $this->input->post('phone'),
                        'buyer_name' => $this->input->post('buyer_name'),     
                        'events_id'=> $this->input->post('events_id'),
						'api_key' => $api_key,
						'auth_token' =>  $auth_token,                       
                        "redirect_url" => base_url('events/do-after-bikerally-payment')                       
                        );
                        // store data to flashdata
                        $this->session->set_flashdata('paymentData',$dataPay);
                        // after storing redirect it to the controller
                        redirect('instamojo/insta-mojo-create');
						//---------------
                    }
                }
        }
    } 
	
	
	
	public function bikerallyEvents()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect("login");
        }
        else
        {
            $data['events'] = $this->events_model->getBikeRallyEvents();
    
            $this->template->write('title', 'All Bike Rally Events', TRUE);
       
            $this->template->add_js('assets/vendors/js/tables/jquery.dataTables.min.js');
            $this->template->add_js('assets/vendors/js/tables/datatable/dataTables.bootstrap4.min.js');
            $this->template->add_js('assets/js/scripts/tables/datatables/datatable-basic.js');
    
            $this->template->add_css('assets/vendors/css/tables/datatable/dataTables.bootstrap4.min.css');
    
            $this->template->write_view('content', 'templates/pages/Events/list-bikerally-events', $data, TRUE);
            $this->template->render();
        }
    } 
	
	public function bikerallyPaymentProcess($e_id)
    {
		// echo "inside ....";
        if($this->session->userdata('user_id') == NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
			//	echo "<pre>";
		//	 print_r($_POST);
			  $data['bikerallyPaymentProcess']=$this->adds_model->addsList(26);
	
      //      $this->form_validation->set_rules('upiTransactionId', 'UPI Transaction Id', 'required');
            //$this->form_validation->set_rules('category_cnt', 'Category Count', 'required');
            $data['e_id'] = $e_id;
            $p_id = $this->session->userdata('user_id');
            $data['checkPaymentWithUPI'] = $this->events_model->checkBikeRallyPaymentWithUPI($e_id,$p_id);
		//	 print_r($data['checkPaymentWithUPI']);
	    // 	  $data['checkPaymentWithUPI'] = $this->events_model->getInfoBikeRallyPaymentDet($e_id,$p_id);
		//	print_r($data['checkPaymentWithUPI']);
		
            $data['events'] = $this->events_model->getSingleBikeRallyEvents($e_id);
			
			
			
			$eventsOrganizer=$data['events'];
			$eventsOrganizerId=$eventsOrganizer['events_organizer'];
			$organizerBikeEventDetails=$this->events_model->getAllEventsByOrganizer($eventsOrganizerId);
			$organizerBikeRallyEventDetails=$this->events_model->getAllBikeRallyEventsByOrganizer($eventsOrganizerId);
			$organizerCarEventDetails=$this->events_model->getAllCarEventsByOrganizer($eventsOrganizerId);
			$data['organizerEventDetails']=array_merge($organizerBikeEventDetails,$organizerBikeRallyEventDetails,$organizerCarEventDetails);
			$dates = array();
			foreach ($data['organizerEventDetails'] as $key => $row) {
			  $dates[$key] = strtotime($row['events_date']);
			}
			// Sort $data based on dates in descending order
			array_multisort($dates, SORT_DESC, $data['organizerEventDetails']);
			
			
			
            $data['eventPaymentRegInfo'] = $this->events_model->getBikeRallyEventPaymentRegInfo($e_id,$p_id);
            $data['eventParticipants'] = $this->events_model->getAllParticipantsbyBikeRallyEventSuccessPayment($e_id);
			$data['eventPendingParticipants'] = $this->events_model->getAllParticipantsbyBikeRallyEventPendingPayment($e_id);
			$data['participants_id']=$this->session->userdata('user_id');
			$data['event_entry_details'] = $this->events_model->getBikeRallyEventEntryDetails($data['participants_id'],$e_id);
			
	//		print_r($_POST);

         /*   if ($this->form_validation->run() == false) 
			{
echo "inside if";
                $this->session->set_flashdata('upiTransactionError', 'Error in UPI Transaction Id submission.Please fill the mandatory fields.');
				
				
            }
            else { */
			
			   if(isset($_POST['upiTransactionId']) != "")
			   {
			   //     echo "in";
			     //   echo "<pre>";
				//	print_r($_POST);
			
						$data['updatePaymentWithUPI'] = $this->events_model->updateBikeRallyPaymentWithUPI();
						$path = 'events/bikerally-payment-process/'.$e_id;	
				//		exit;
						redirect(base_url($path));
			   }
           /* }*/
			
            $data['title'] = 'Payment Process';
            $this->template->write('title', 'Payment Process', TRUE);  
            $this->template->add_css('assets/css/style2.css');
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');
            $this->template->write_view('content', 'templates/pages/Events/bikerally-payment-process', $data, TRUE);
            $this->template->render();
       
        }
    } 
	
	public function bikerallyPaymentUPIProcessing($e_id)
    {
        if($this->session->userdata('user_id') == NULL)
        {
            redirect(base_url('login'));
            
        }
        else
        {
            $data['title'] = 'Payment UPI Processing';
            $p_id = $this->session->userdata('user_id');
            $data['eventType'] = "Bike Rally";

            $data['events'] = $this->events_model->getSingleBikeRallyEvents($e_id);
			$eventsOrganizer=$data['events'];
			$eventsOrganizerId=$eventsOrganizer['events_organizer'];
			$organizerBikeEventDetails=$this->events_model->getAllEventsByOrganizer($eventsOrganizerId);
			$organizerBikeRallyEventDetails=$this->events_model->getAllBikeRallyEventsByOrganizer($eventsOrganizerId);
			$organizerCarEventDetails=$this->events_model->getAllCarEventsByOrganizer($eventsOrganizerId);
			$data['organizerEventDetails']=array_merge($organizerBikeEventDetails,$organizerBikeRallyEventDetails,$organizerCarEventDetails);
			$dates = array();
			foreach ($data['organizerEventDetails'] as $key => $row)
			{
			   $dates[$key] = strtotime($row['events_date']);
			}
			// Sort $data based on dates in descending order
			array_multisort($dates, SORT_DESC, $data['organizerEventDetails']);	

			
			//$data['orderedCategoryLists']['categories_prices'] = [];
			//echo '<pre>';print_r($data['orderedCategoryLists']); exit;
            if($data['events']==NULL)
            $data['events']['categories']= array();        
            //$data['participants_code']=array_diff($a,$participantsCode['participants_code']);
            $data['eventPaymentRegInfo'] = $this->events_model->getBikeRallyEventPaymentRegInfo($e_id,$p_id);
            $data['eventParticipants'] = $this->events_model->getAllParticipantsbyBikeRallyEventSuccessPayment($e_id);
			$data['eventPendingParticipants'] = $this->events_model->getAllParticipantsbyBikeRallyEventPendingPayment($e_id);
            // print_r($data['eventPaymentRegInfo']);exit;

            $this->template->write('title', 'Payment UPI Processing', TRUE);  


            


            $this->template->add_css('assets/css/style2.css');

          
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');

            //$this->template->add_js('assets/js/bootstrap-select.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');

            $this->template->write_view('content', 'templates/pages/Events/payment-upi-processing', $data, TRUE);


            $this->template->render();
        }
    }
	
	 public function confirmBikeRallyUPIPaymentProcess()
    {

        echo $status = $this->events_model->confirmBikeRallyUPIPaymentProcess();
    }
    
	
	public function listParticipantsByBikeRallyEvents()
    {
    //    $data['title'] = 'Bike Rally Eventwise Participants List';
        $data['events'] = $this->events_model->getBikeRallyEvents();
        $this->template->write('title', 'Bike Rally Eventwise Participants List', TRUE);          
        $this->template->add_js('assets/vendors/js/tables/jquery.dataTables.min.js');
        $this->template->add_js('assets/vendors/js/tables/datatable/dataTables.bootstrap4.min.js');
        $this->template->add_js('assets/js/scripts/tables/datatables/datatable-basic.js');
        $this->template->add_js('assets/vendors/js/jquery.PrintArea.js');
        $this->template->add_css('assets/vendors/css/tables/datatable/dataTables.bootstrap4.min.css');
        $this->template->add_css('assets/css/style.css');
        $this->template->write_view('content', 'templates/pages/Events/list-bikerally-event-participants', $data, TRUE);
        $this->template->render();
    }
	
	public function listParticipantsByBikeRallyEventsAjax()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $id = $this->input->post('event_id');
            $data['cat_id'] = $this->input->post('category');
            $data['events_id'] = $id;

            $data['events'] = $this->events_model->getSingleBikeRallyEvents($id);
            $data['participants'] = $this->events_model->getAllParticipantsbyBikeRallyEventAndCategory($id,$data['cat_id']);    
            $this->load->view('templates/pages/Events/list-bikerally-event-participants-ajax', $data);
        }
    }
	
	public function bikerallyCategoryOfEvent()
	{
		 $Categories = $this->events_model->getBikeRallyCategoryOfEvent();
		 echo'<option value="">---Select Category---</option>';
		 foreach($Categories as $Category)
		 {
			 echo '<option value="'.$Category["categories_id"].'">'.$Category["categories_name"].'</option>';
		 }
	}

   public function bikerallyPaymentConfirm($e_id)
    {
        if($this->session->userdata('user_id') == NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
			$data['paymentConfirm']=$this->adds_model->addsList(29);
            $data['title'] = 'Payment Confirm';
            $p_id = $this->session->userdata('user_id');
            $data['eventPaymentRegInfo'] = $this->events_model->getBikeRallyEventPaymentRegInfo($e_id,$p_id);
    //        $data['eventParticipants'] = $this->events_model->getAllParticipantsbyEventSuccessPayment($e_id);
	//		$data['eventPendingParticipants'] = $this->events_model->getAllParticipantsbyEventPendingPayment($e_id);
			$data['eventParticipants'] = $this->events_model->getAllParticipantsbyBikeRallyEventSuccessPayment($e_id);
			$data['eventPendingParticipants'] = $this->events_model->getAllParticipantsbyBikeRallyEventPendingPayment($e_id);
			
			
			
			
			
			
		//	print_r($data['eventParticipants']);
		//	exit;
			
			$data['events'] = $this->events_model->getSingleBikeRallyEvents($e_id);
			
			$eventsOrganizer=$data['events'];
			$eventsOrganizerId=$eventsOrganizer['events_organizer'];
			$organizerBikeEventDetails=$this->events_model->getAllEventsByOrganizer($eventsOrganizerId);
			$organizerBikeRallyEventDetails=$this->events_model->getAllBikeRallyEventsByOrganizer($eventsOrganizerId);
			$organizerCarEventDetails=$this->events_model->getAllCarEventsByOrganizer($eventsOrganizerId);
			$data['organizerEventDetails']=array_merge($organizerBikeEventDetails,$organizerBikeRallyEventDetails,$organizerCarEventDetails);
			$dates = array();
			foreach ($data['organizerEventDetails'] as $key => $row) {
			  $dates[$key] = strtotime($row['events_date']);
			}
			// Sort $data based on dates in descending order
			array_multisort($dates, SORT_DESC, $data['organizerEventDetails']);
			
			
            $data['eventType'] = "Bike Rally";
            // print_r($data['eventPaymentRegInfo']);exit;
            $this->template->write('title', 'Payment Confirm', TRUE);  
            $this->template->add_css('assets/css/style2.css');          
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js'); 
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');
			

			$data['participants_id']=$p_id;
			$data['participants_events_id']=$e_id;
			$data['events_id']=$e_id;
            $this->template->write_view('content', 'templates/pages/Events/payment-confirm', $data, TRUE);


            $this->template->render();
        }
    }
	
	public function createBikeRallyExcel()
	{
		if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
				$event_id = $this->input->post('event_id');
				$cat_id = $this->input->post('cat_id');
				$events = $this->events_model->getSingleBikeRallyEvents($event_id);   // get all detals of the specified event
				
				
				$eventParticipants = $this->events_model->getBikeRallyEventParticipantsList($event_id); 
				$filename = $events['events_name'];
				$spreadsheet = new Spreadsheet();
				$sheet = $spreadsheet->getActiveSheet();
				$rowPosNo=1;
				
				
				$spreadsheet
				->getActiveSheet()
				->getStyle('A1:AE1')
				->getFill()
				->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
				->getStartColor()
				->setARGB('ccffcc');
				
				
			 /* $styleArray = [
							'font' => [
								'bold' => true,
							],
							'alignment' => [
									'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
									'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
								],
//							'fill' => [
//								'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
//								'startColor' => [
//									'argb' => '0070C0',
//								],
//								'endColor' => [
//									'argb' => '0070C0',
//								],
//							],
						];*/

				$spreadsheet->getDefaultStyle()->getFont()->setSize(12);
				
				$sheet->setCellValue('A'.$rowPosNo, 'SUBMISSION DATE');     // A 
				$sheet->setCellValue('B'.$rowPosNo, 'NAME');  // B
				$sheet->setCellValue('C'.$rowPosNo, 'CITY');     // C   // X   .......   BOTH CONTAINS CITY FIELD
				$sheet->setCellValue('D'.$rowPosNo, 'PHONE NO');   // D
				$sheet->setCellValue('E'.$rowPosNo, 'COMPETITION NUMBER');    // E
				$sheet->setCellValue('F'.$rowPosNo, 'CLASSES TO RIDE');       // F
				$sheet->setCellValue('G'.$rowPosNo, 'IS RIDING FOR CHAMPIONSHIP');   // G
				$sheet->setCellValue('H'.$rowPosNo, 'FMSCI LIC NO');   // H
				$sheet->setCellValue('I'.$rowPosNo, 'DRIVING LIC NO ');    // I 
				$sheet->setCellValue('J'.$rowPosNo, 'AMOUNT');   // J
				$sheet->setCellValue('K'.$rowPosNo, 'STATUS ');  // K
				$sheet->setCellValue('L'.$rowPosNo, 'REMARKS'); // L
				$sheet->setCellValue('M'.$rowPosNo, 'CONFIRMING DATE'); // M
				$sheet->setCellValue('N'.$rowPosNo, 'REGN. NO '); // N
				$sheet->setCellValue('O'.$rowPosNo, 'CUBIC CAPACITY'); // O
				$sheet->setCellValue('P'.$rowPosNo, 'MAKE'); // P
				$sheet->setCellValue('Q'.$rowPosNo, 'MODEL ');  // Q
				$sheet->setCellValue('R'.$rowPosNo, 'ENGINE NO');  // R
				$sheet->setCellValue('S'.$rowPosNo, 'CHASSIS NO');  // S
				$sheet->setCellValue('T'.$rowPosNo, 'VEHICLE SHARING RIDER');    // T
				$sheet->setCellValue('U'.$rowPosNo, 'NAME OF TUNER'); // U
				$sheet->setCellValue('V'.$rowPosNo, 'TEAM NAME '); // V
				$sheet->setCellValue('W'.$rowPosNo, 'ADDRESS ');  // W
				$sheet->setCellValue('X'.$rowPosNo, 'CITY');      // X   .......   BOTH CONTAINS CITY FIELD
				$sheet->setCellValue('Y'.$rowPosNo, 'STATE');  // Y
				$sheet->setCellValue('Z'.$rowPosNo, 'PIN');  // Z
				$sheet->setCellValue('AA'.$rowPosNo, 'EMAIL'); // AA1
				$sheet->setCellValue('AB'.$rowPosNo, 'DATE OF BIRTH');  // AB1
				$sheet->setCellValue('AC'.$rowPosNo, 'BLOOD GROUP');  // AC1
				$sheet->setCellValue('AD'.$rowPosNo, 'EMERGENCY CONTACT PERSON');  // AD1
				$sheet->setCellValue('AE'.$rowPosNo, 'EMERGENCY CONTACT PERSON PHONE NO');   // AE1
				
				
				
				foreach($eventParticipants as $participant)
				{
					//   $participantsBillDet=$this->events_model->getBikeRallyParticipantsBillDet($participant['events_details_participants_id'],$participant['events_details_events_id']);
						 $paymentDet=$this->events_model->getBikeRallyPaymentDet($participant['events_details_participants_id'],$participant['events_details_events_id']);
						 $competitionNos=$this->events_model->getParticipantsBikeRallyCompetetions($participant['events_details_participants_id'],$participant['events_details_events_id']);
						 $competitionNoDet=explode(",",$competitionNos);
						 $rowPosNo=$rowPosNo + 1;
						 $sheet->setCellValue('A'.$rowPosNo, $paymentDet['payment_date']);  
						 $sheet->setCellValue('B'.$rowPosNo,strtoupper($participant['participants_name'])); 
						 $sheet->setCellValue('C'.$rowPosNo,$participant['participants_city']); 
						 $sheet->setCellValue('X'.$rowPosNo,$participant['participants_city']); 
						 $sheet->setCellValue('D'.$rowPosNo,$participant['participants_phone']); 
						 $sheet->setCellValue('AC'.$rowPosNo,$participant['participants_blood_group']); 
						 $sheet->setCellValue('W'.$rowPosNo,$participant['participants_address']); 
					     $sheet->setCellValue('Z'.$rowPosNo,$participant['participants_post']);   
						 $sheet->setCellValue('Y'.$rowPosNo,$participant['participants_state']); 
						 $sheet->setCellValue('AA'.$rowPosNo,$participant['participants_email']); 
						 $sheet->setCellValue('AB'.$rowPosNo,$participant['participants_dob']); 
						 $sheet->setCellValue('I'.$rowPosNo,$participant['participants_civil_license_number']); 
						 $sheet->setCellValue('H'.$rowPosNo,strtoupper($participant['participants_fmsci_license_number'])); 
						 $sheet->setCellValue('AD'.$rowPosNo,$participant['participants_emergency_contact_person']); 
						 $sheet->setCellValue('AE'.$rowPosNo,$participant['participants_emergency_contact_phone']); 
						 $sheet->setCellValue('U'.$rowPosNo,$participant['events_details_name_of_tuner']); 
						 $sheet->setCellValue('V'.$rowPosNo,$participant['events_details_participants_team_name']); 
						
						
						// collect category ids list and select one category and get the details of that category
						// $bikeRallyEventVehicleDetails=$this->events_model->getBikeRallyEventDetVehicleDet($event_id,$participant['events_details_category_id'],
//						 $participant['events_details_participants_id']);
						 
						 $bikeRallyEventVehicleDetails=$this->events_model->getBikeRallyEventDetVehicleDet($event_id, $participant['events_details_participants_id']);
						 
						 $intCnt=0;
						 $compNoPos=-1;
						 foreach($bikeRallyEventVehicleDetails as $bikeRallyEventVehicle)
						 {
						    $intCnt=$intCnt+1;
							$compNoPos=$compNoPos+1;
						    $catDet=$this->events_model->getCategoryName($bikeRallyEventVehicle['vehicle_details_category_id']);
							$category_name=$catDet['categories_name'];
						 	$sheet->setCellValue('F'.$rowPosNo, $category_name);    
							$sheet->setCellValue('N'.$rowPosNo, $bikeRallyEventVehicle['vehicle_details_number']);
							$sheet->setCellValue('Q'.$rowPosNo, $bikeRallyEventVehicle['vehicle_details_model']);
							$sheet->setCellValue('R'.$rowPosNo, $bikeRallyEventVehicle['vehicle_details_engine_no']);
							$sheet->setCellValue('T'.$rowPosNo,$bikeRallyEventVehicle['vehicle_sharing_rider']); 
							$sheet->setCellValue('O'.$rowPosNo,$bikeRallyEventVehicle['vehicle_details_cc']); 
							$sheet->setCellValue('P'.$rowPosNo,$bikeRallyEventVehicle['vehicle_details_make']); 
							$sheet->setCellValue('S'.$rowPosNo,$bikeRallyEventVehicle['vehicle_details_chassis_no']); 
					 		$sheet->setCellValue('E'.$rowPosNo,(int)$competitionNoDet[$compNoPos]); 
							if($bikeRallyEventVehicle['is_riding_for_championship'] == 1)
							   $rideChampionship="Yes";
							else
							   $rideChampionship="No";
							
							 $sheet->setCellValue('G'.$rowPosNo,$rideChampionship); 
							 
							$rowPosNo=$rowPosNo + 1;
					     }
						//	$rowPosNo=$rowPosNo - 1;
							$rowPosNo=$rowPosNo-$intCnt;
							$sheet->setCellValue('J'.$rowPosNo, $paymentDet['payment_amount']); 
							$sheet->setCellValue('M'.$rowPosNo, $paymentDet['payment_date']);
							$payment_id=$paymentDet['payment_id'];
							$upiPaymentDet=$this->events_model->getBikeRallyUpiPaymentDet($payment_id);
							if($paymentDet['payment_status'] == 1)
							{
						  	   $paymentStatus="Confirm";
							}
							else if($paymentDet['payment_status'] == 0)
							{
						  	   $paymentStatus="Pending";
							}
							else
							{
							   $paymentStatus="Pending";
							}
							
							$sheet->setCellValue('K'.$rowPosNo, $paymentStatus);
							$sheet->setCellValue('L'.$rowPosNo, $upiPaymentDet['upi_details_comments']);
							
						
							$rowPosNo=$rowPosNo+($intCnt-1);
					
				}
				
				
				
		//		$data['events_det'] = $this->events_model->getSingleBikeRallyEvents($event_id); // $participants_events_id);
				
				
			foreach ($sheet->getColumnIterator() as $column) 
			{
   					$sheet->getColumnDimension($column->getColumnIndex())->setAutoSize(true);
			}
				
				$writer = new Xlsx($spreadsheet);
				$filename = $events['events_name'];
				
			//	$writer->save($filename);  // will create and save the file in the root of the project
				
				
				header('Content-Type: application/vnd.ms-excel');
				header('Content-Disposition: attachment;filename="'. $filename .'.xlsx"');
				header('Cache-Control: max-age=0');
				$writer->save('php://output'); // download file
		 }
    } 
	
	
	
	
	
		

	
	public function createCarExcel()
	{
		if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
				
				$event_id = $this->input->post('event_id');
				$cat_id = $this->input->post('cat_id');
				$events = $this->events_model->getSingleCarEvents($event_id);   // get all detals of the specified event
				$eventParticipants = $this->events_model->getCarEventParticipantsList($event_id);
				$filename = $events['events_name'];
				$spreadsheet = new Spreadsheet();
				$sheet = $spreadsheet->getActiveSheet();
				$rowPosNo=1;
				
				
				$spreadsheet
				->getActiveSheet()
				->getStyle('A1:AC1')
				->getFill()
				->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
				->getStartColor()
				->setARGB('ccffcc');
				
				$spreadsheet->getDefaultStyle()->getFont()->setSize(12);
				
				$sheet->setCellValue('A'.$rowPosNo, 'SUBMISSION DATE');     
				$sheet->setCellValue('B'.$rowPosNo, 'NAME');  
				$sheet->setCellValue('C'.$rowPosNo, 'CITY');     
				$sheet->setCellValue('D'.$rowPosNo, 'PHONE NO');   
				$sheet->setCellValue('E'.$rowPosNo, 'COMPETITION NUMBER');   
				$sheet->setCellValue('F'.$rowPosNo, 'CLASSES TO RIDE');    
				$sheet->setCellValue('G'.$rowPosNo, 'SPECIAL PACKAGE');        
				$sheet->setCellValue('H'.$rowPosNo, 'FMSCI LIC NO');   
				$sheet->setCellValue('I'.$rowPosNo, 'DRIVING LIC NO ');    
				$sheet->setCellValue('J'.$rowPosNo, 'AMOUNT');  
				$sheet->setCellValue('K'.$rowPosNo, 'STATUS ');  
				$sheet->setCellValue('L'.$rowPosNo, 'REMARKS'); 
				$sheet->setCellValue('M'.$rowPosNo, 'CONFIRMING DATE'); 
				$sheet->setCellValue('N'.$rowPosNo, 'REGN. NO '); 
				$sheet->setCellValue('O'.$rowPosNo, 'CUBIC CAPACITY'); 
				$sheet->setCellValue('P'.$rowPosNo, 'MAKE'); 
				$sheet->setCellValue('Q'.$rowPosNo, 'MODEL ');  
				$sheet->setCellValue('R'.$rowPosNo, 'TEAM NAME '); 
				$sheet->setCellValue('S'.$rowPosNo, 'TEAM MATE '); 
				$sheet->setCellValue('T'.$rowPosNo, 'ADDRESS ');  
				$sheet->setCellValue('U'.$rowPosNo, 'CITY');      
				$sheet->setCellValue('V'.$rowPosNo, 'STATE');  
				$sheet->setCellValue('W'.$rowPosNo, 'PIN'); 
				$sheet->setCellValue('X'.$rowPosNo, 'EMAIL'); 
				$sheet->setCellValue('Y'.$rowPosNo, 'DATE OF BIRTH');  
				$sheet->setCellValue('Z'.$rowPosNo, 'BLOOD GROUP');  
				$sheet->setCellValue('AA'.$rowPosNo, 'EMERGENCY CONTACT PERSON');  
				$sheet->setCellValue('AB'.$rowPosNo, 'EMERGENCY CONTACT PERSON PHONE NO');   
				
				
				foreach($eventParticipants as $participant)
				{
					$paymentDet=$this->events_model->getCarPaymentDet($participant['events_details_participants_id'],$participant['events_details_events_id']);
					$competitionNos=$this->events_model->getCarParticipantsCompetetions($participant['events_details_participants_id'],$participant['events_details_events_id']);
					//	 $competitionNoDet=substr($competitionNos, 0, -2);
					 	 $competitionNoDet=explode(",",$competitionNos);
						 $rowPosNo=$rowPosNo + 1;
						 $sheet->setCellValue('A'.$rowPosNo, $paymentDet['payment_date']);  
						 $sheet->setCellValue('B'.$rowPosNo,strtoupper($participant['participants_name'])); 
						 $sheet->setCellValue('C'.$rowPosNo,$participant['participants_city']); 
						 $sheet->setCellValue('U'.$rowPosNo,$participant['participants_city']); 
						 $sheet->setCellValue('D'.$rowPosNo,$participant['participants_phone']); 
						 
						 if($participant['events_details_special_package'] == 1)
						   $specialPackage="Yes";
						 else
						   $specialPackage="No";
							
						 $sheet->setCellValue('G'.$rowPosNo,$specialPackage);
						 $sheet->setCellValue('Z'.$rowPosNo,$participant['participants_blood_group']); 
						 $sheet->setCellValue('T'.$rowPosNo,$participant['participants_address']); 
					     $sheet->setCellValue('W'.$rowPosNo,$participant['participants_post']);   
						 $sheet->setCellValue('V'.$rowPosNo,$participant['participants_state']); 
						 $sheet->setCellValue('X'.$rowPosNo,$participant['participants_email']); 
						 $sheet->setCellValue('Y'.$rowPosNo,$participant['participants_dob']); 
						 $sheet->setCellValue('I'.$rowPosNo,$participant['participants_civil_license_number']); 
						 $sheet->setCellValue('H'.$rowPosNo,strtoupper($participant['participants_fmsci_license_number'])); 
						 $sheet->setCellValue('AA'.$rowPosNo,$participant['participants_emergency_contact_person']); 
						 $sheet->setCellValue('AB'.$rowPosNo,$participant['participants_emergency_contact_phone']); 
						 $sheet->setCellValue('R'.$rowPosNo,$participant['events_details_participants_team_name']); 
						 $sheet->setCellValue('S'.$rowPosNo,$participant['events_details_participants_team_mate']); 
						
						// collect category ids list and select one category and get the details of that category
						$carEventVehicleDetails=$this->events_model->getCarEventDetVehicleDet($event_id, $participant['events_details_participants_id']);
						 
						
						 
						 $intCnt=0;
						 $compNoPos=-1;
						 foreach($carEventVehicleDetails as $carEventVehicle)
						 {
						    $intCnt=$intCnt+1;
							$compNoPos=$compNoPos+1;
						    $catDet=$this->events_model->getCarCategoryName($carEventVehicle['vehicle_details_category_id']);
							
							$category_name=$catDet['categories_name'];
						 	$sheet->setCellValue('F'.$rowPosNo, $category_name);    
							$sheet->setCellValue('N'.$rowPosNo, $carEventVehicle['vehicle_details_number']);
							$sheet->setCellValue('Q'.$rowPosNo, $carEventVehicle['vehicle_details_model']);
							$sheet->setCellValue('O'.$rowPosNo,$carEventVehicle['vehicle_details_cc']); 
							$sheet->setCellValue('P'.$rowPosNo,$carEventVehicle['vehicle_details_make']); 
					 //		$sheet->setCellValue('E'.$rowPosNo,$competitionNoDet);     ////////////////////////////////
							$sheet->setCellValue('E'.$rowPosNo,(int)$competitionNoDet[$compNoPos]); 
							
							
							 
							$rowPosNo=$rowPosNo + 1;
					     }
							$rowPosNo=$rowPosNo-$intCnt;
						
							$sheet->setCellValue('J'.$rowPosNo, $paymentDet['payment_amount']); 
							$sheet->setCellValue('M'.$rowPosNo, $paymentDet['payment_date']);
							$payment_id=$paymentDet['payment_id'];
							$upiPaymentDet=$this->events_model->getCarUpiPaymentDet($payment_id);
					//		echo "upi payment det = " . $upiPaymentDet;
							
								
							if($paymentDet['payment_status'] == 1)
							{
						  	   $paymentStatus="Confirm";
							}
							else if($paymentDet['payment_status'] == 0)
							{
						  	   $paymentStatus="Pending";
							}
							else
							{
							   $paymentStatus="Pending";
							}
							
							
							$sheet->setCellValue('K'.$rowPosNo, $paymentStatus);
							$sheet->setCellValue('L'.$rowPosNo, $upiPaymentDet['upi_details_comments']);
							
						
							$rowPosNo=$rowPosNo+($intCnt-1);
					
				}
			//	exit;
				
			foreach ($sheet->getColumnIterator() as $column) 
			{
   					$sheet->getColumnDimension($column->getColumnIndex())->setAutoSize(true);
			} 
				
				$writer = new Xlsx($spreadsheet);
				$filename = $events['events_name'];
				
				$writer->save($filename);  // will create and save the file in the root of the project
				
				
				header('Content-Type: application/vnd.ms-excel');
				header('Content-Disposition: attachment;filename="'. $filename .'.xlsx"');
				header('Cache-Control: max-age=0');
				$writer->save('php://output'); // download file
		 }
    }  
	
	
	
	public function createTrainingProgramExcel()
	{
		if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
				
				$event_id = $this->input->post('event_id');
				$cat_id = $this->input->post('cat_id');
				$events = $this->events_model->getSingleTrainingProgramEvents($event_id);   // get all detals of the specified event
				$eventParticipants = $this->events_model->getTrainingProgramEventParticipantsList($event_id);
				$filename = $events['events_name'];
				$spreadsheet = new Spreadsheet();
				$sheet = $spreadsheet->getActiveSheet();
				$rowPosNo=1;
				
				
				$spreadsheet
				->getActiveSheet()
				->getStyle('A1:AA1')
				->getFill()
				->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
				->getStartColor()
				->setARGB('ccffcc');
				
				$spreadsheet->getDefaultStyle()->getFont()->setSize(12);
				
				$sheet->setCellValue('A'.$rowPosNo, 'SUBMISSION DATE');     
				$sheet->setCellValue('B'.$rowPosNo, 'NAME');  
				$sheet->setCellValue('C'.$rowPosNo, 'CITY');     
				$sheet->setCellValue('D'.$rowPosNo, 'PHONE NO');   
				$sheet->setCellValue('E'.$rowPosNo, 'CLASSES TO RIDE');    
				$sheet->setCellValue('F'.$rowPosNo, 'SPECIAL PACKAGE');        
				$sheet->setCellValue('G'.$rowPosNo, 'FMSCI LIC NO');   
				$sheet->setCellValue('H'.$rowPosNo, 'DRIVING LIC NO ');    
				$sheet->setCellValue('I'.$rowPosNo, 'AMOUNT');  
				$sheet->setCellValue('J'.$rowPosNo, 'STATUS ');  
				$sheet->setCellValue('K'.$rowPosNo, 'REMARKS'); 
				$sheet->setCellValue('L'.$rowPosNo, 'CONFIRMING DATE'); 
				$sheet->setCellValue('M'.$rowPosNo, 'REGN. NO '); 
				$sheet->setCellValue('N'.$rowPosNo, 'CUBIC CAPACITY'); 
				$sheet->setCellValue('O'.$rowPosNo, 'MAKE'); 
				$sheet->setCellValue('P'.$rowPosNo, 'MODEL ');  
				$sheet->setCellValue('Q'.$rowPosNo, 'TEAM NAME '); 
				$sheet->setCellValue('R'.$rowPosNo, 'TEAM MATE '); 
				$sheet->setCellValue('S'.$rowPosNo, 'ADDRESS ');  
				$sheet->setCellValue('T'.$rowPosNo, 'CITY');      
				$sheet->setCellValue('U'.$rowPosNo, 'STATE');  
				$sheet->setCellValue('V'.$rowPosNo, 'PIN'); 
				$sheet->setCellValue('W'.$rowPosNo, 'EMAIL'); 
				$sheet->setCellValue('X'.$rowPosNo, 'DATE OF BIRTH');  
				$sheet->setCellValue('Y'.$rowPosNo, 'BLOOD GROUP');  
				$sheet->setCellValue('Z'.$rowPosNo, 'EMERGENCY CONTACT PERSON');  
				$sheet->setCellValue('AA'.$rowPosNo, 'EMERGENCY CONTACT PERSON PHONE NO');   
				
				
				foreach($eventParticipants as $participant)
				{
					$paymentDet=$this->events_model->getTrainingProgramPaymentDet($participant['events_details_participants_id'],$participant['events_details_events_id']);
					$competitionNos=$this->events_model->getTrainingProgramParticipantsCompetetions($participant['events_details_participants_id'],$participant['events_details_events_id']);
					//	 $competitionNoDet=substr($competitionNos, 0, -2);
					 	 $competitionNoDet=explode(",",$competitionNos);
						 $rowPosNo=$rowPosNo + 1;
						 $sheet->setCellValue('A'.$rowPosNo, $paymentDet['payment_date']);  
						 $sheet->setCellValue('B'.$rowPosNo,strtoupper($participant['participants_name'])); 
						 $sheet->setCellValue('C'.$rowPosNo,$participant['participants_city']); 
						 $sheet->setCellValue('T'.$rowPosNo,$participant['participants_city']); 
						 $sheet->setCellValue('D'.$rowPosNo,$participant['participants_phone']); 
						 
						 if($participant['events_details_special_package'] == 1)
						   $specialPackage="Yes";
						 else
						   $specialPackage="No";
							
						 $sheet->setCellValue('F'.$rowPosNo,$specialPackage);
						 $sheet->setCellValue('Y'.$rowPosNo,$participant['participants_blood_group']); 
						 $sheet->setCellValue('S'.$rowPosNo,$participant['participants_address']); 
					     $sheet->setCellValue('V'.$rowPosNo,$participant['participants_post']);   
						 $sheet->setCellValue('U'.$rowPosNo,$participant['participants_state']); 
						 $sheet->setCellValue('W'.$rowPosNo,$participant['participants_email']); 
						 $sheet->setCellValue('X'.$rowPosNo,$participant['participants_dob']); 
						 $sheet->setCellValue('H'.$rowPosNo,$participant['participants_civil_license_number']); 
						 $sheet->setCellValue('G'.$rowPosNo,strtoupper($participant['participants_fmsci_license_number'])); 
						 $sheet->setCellValue('Z'.$rowPosNo,$participant['participants_emergency_contact_person']); 
						 $sheet->setCellValue('AA'.$rowPosNo,$participant['participants_emergency_contact_phone']); 
						 $sheet->setCellValue('R'.$rowPosNo,$participant['events_details_participants_team_name']); 
						 $sheet->setCellValue('S'.$rowPosNo,$participant['events_details_participants_team_mate']); 
					
						// collect category ids list and select one category and get the details of that category
						$trainingprogramEventVehicleDetails=$this->events_model->getTrainingProgramEventDetVehicleDet($event_id, $participant['events_details_participants_id']);
						 
						 $intCnt=0;
						 $compNoPos=-1;
						 foreach($trainingprogramEventVehicleDetails as $trainingprogramEventVehicle)
						 {
						    $intCnt=$intCnt+1;
							$compNoPos=$compNoPos+1;
							
						    $catDet=$this->events_model->getTrainingProgramCategoryName($trainingprogramEventVehicle['vehicle_details_category_id']);
							
							$category_name=$catDet['categories_name'];
							
						 	$sheet->setCellValue('E'.$rowPosNo, $category_name);    
							$sheet->setCellValue('M'.$rowPosNo, $trainingprogramEventVehicle['vehicle_details_number']);
							$sheet->setCellValue('P'.$rowPosNo, $trainingprogramEventVehicle['vehicle_details_model']);
							
							$sheet->setCellValue('N'.$rowPosNo,$trainingprogramEventVehicle['vehicle_details_cc']); 
							$sheet->setCellValue('O'.$rowPosNo,$trainingprogramEventVehicle['vehicle_details_make']); 
							$rowPosNo=$rowPosNo + 1;
					     }
							$rowPosNo=$rowPosNo-$intCnt;
						
							$sheet->setCellValue('I'.$rowPosNo, $paymentDet['payment_amount']); 
							$sheet->setCellValue('L'.$rowPosNo, $paymentDet['payment_date']);
							$payment_id=$paymentDet['payment_id'];
							
							$upiPaymentDet=$this->events_model->getTrainingProgramUpiPaymentDet($payment_id);
							
							if($paymentDet['payment_status'] == 1)
							{
						  	   $paymentStatus="Confirm";
							}
							else if($paymentDet['payment_status'] == 0)
							{
						  	   $paymentStatus="Pending";
							}
							else
							{
							   $paymentStatus="Pending";
							}
							
							
							$sheet->setCellValue('J'.$rowPosNo, $paymentStatus);
							$sheet->setCellValue('K'.$rowPosNo, $upiPaymentDet['upi_details_comments']);
							
						
							$rowPosNo=$rowPosNo+($intCnt-1);
					
				}
			//	exit;
				
			foreach ($sheet->getColumnIterator() as $column) 
			{
   					$sheet->getColumnDimension($column->getColumnIndex())->setAutoSize(true);
			} 
				
				$writer = new Xlsx($spreadsheet);
				$filename = $events['events_name'];
				
				$writer->save($filename);  // will create and save the file in the root of the project
				
				
				header('Content-Type: application/vnd.ms-excel');
				header('Content-Disposition: attachment;filename="'. $filename .'.xlsx"');
				header('Cache-Control: max-age=0');
				$writer->save('php://output'); // download file
		 }
    } 
	
	
	public function deleteBikerallyParticipant()
	{
	    $event_id=$_POST['event_id'];
	    $participant_id=$_POST['participant_id'];
		if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
			$this->participants_model->deleteBikerallyParticipant($event_id,$participant_id);
			echo "Successfully Deleted";
		}
	}
	
	
	public function deleteBikeParticipant()
	{
	    $event_id=$_POST['event_id'];
	    $participant_id=$_POST['participant_id'];
		if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
			$this->participants_model->deleteBikeParticipant($event_id,$participant_id);
			echo "Successfully Deleted";
		}
	}
	
	public function deleteCarParticipant()
	{
	    $event_id=$_POST['event_id'];
	    $participant_id=$_POST['participant_id'];
		if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
			$this->participants_model->deleteCarParticipant($event_id,$participant_id);
			echo "Successfully Deleted";
		}
	}
	
	
	public function deleteTrainingProgramParticipant()
	{
	    $event_id=$_POST['event_id'];
	    $participant_id=$_POST['participant_id'];
		if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
			$this->participants_model->deleteTrainingProgramParticipant($event_id,$participant_id);
			echo "Successfully Deleted";
		}
	}
	
	
	public function createEventsExcel()
	{
		if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
				
				$event_id = $this->input->post('event_id');
				$cat_id = $this->input->post('cat_id');
				$events = $this->events_model->getSingleEvents($event_id);   // get all detals of the specified event
				$eventParticipants = $this->events_model->getEventParticipantsList($event_id);
				
			/*	echo "<pre>";
				print_r($eventParticipants);*/
				$filename = $events['events_name'];
				$spreadsheet = new Spreadsheet();
				$sheet = $spreadsheet->getActiveSheet();
				$rowPosNo=1;
				
				
				$spreadsheet
				->getActiveSheet()
				->getStyle('A1:AC1')
				->getFill()
				->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
				->getStartColor()
				->setARGB('ccffcc');
				
				$spreadsheet->getDefaultStyle()->getFont()->setSize(12);
				
				$sheet->setCellValue('A'.$rowPosNo, 'SUBMISSION DATE');     
				$sheet->setCellValue('B'.$rowPosNo, 'NAME');  
				$sheet->setCellValue('C'.$rowPosNo, 'CITY');     
				$sheet->setCellValue('D'.$rowPosNo, 'PHONE NO');   
				$sheet->setCellValue('E'.$rowPosNo, 'COMPETITION NUMBER');   
				$sheet->setCellValue('F'.$rowPosNo, 'CLASSES TO RIDE');    
				$sheet->setCellValue('G'.$rowPosNo, 'FMSCI LIC NO');   
				$sheet->setCellValue('H'.$rowPosNo, 'DRIVING LIC NO ');    
				$sheet->setCellValue('I'.$rowPosNo, 'AMOUNT');
				$sheet->setCellValue('J'.$rowPosNo, 'UPI Transaction ID');
				$sheet->setCellValue('K'.$rowPosNo, 'STATUS ');  
				$sheet->setCellValue('L'.$rowPosNo, 'REMARKS'); 
				$sheet->setCellValue('M'.$rowPosNo, 'CONFIRMING DATE'); 
				$sheet->setCellValue('N'.$rowPosNo, 'REGN. NO '); 
				$sheet->setCellValue('O'.$rowPosNo, 'MAKE'); 
				$sheet->setCellValue('P'.$rowPosNo, 'MODEL');
				$sheet->setCellValue('Q'.$rowPosNo, 'ENGINE NO');
				$sheet->setCellValue('R'.$rowPosNo, 'CHASSIS NO');	
				$sheet->setCellValue('S'.$rowPosNo, 'NAME OF TUNER');	
				$sheet->setCellValue('T'.$rowPosNo, 'TEAM NAME '); 
				$sheet->setCellValue('U'.$rowPosNo, 'ADDRESS ');  
				$sheet->setCellValue('V'.$rowPosNo, 'CITY');      
				$sheet->setCellValue('W'.$rowPosNo, 'STATE');  
				$sheet->setCellValue('X'.$rowPosNo, 'PIN'); 
				$sheet->setCellValue('Y'.$rowPosNo, 'EMAIL'); 
				$sheet->setCellValue('Z'.$rowPosNo, 'DATE OF BIRTH');  
				$sheet->setCellValue('AA'.$rowPosNo, 'BLOOD GROUP');  
				$sheet->setCellValue('AB'.$rowPosNo, 'EMERGENCY CONTACT PERSON');  
				$sheet->setCellValue('AC'.$rowPosNo, 'EMERGENCY CONTACT PERSON PHONE NO');   
				
				
				foreach($eventParticipants as $participant)
				{
					$paymentDet=$this->events_model->getPaymentDet($participant['events_details_participants_id'],$participant['events_details_events_id']);
					$competitionNos=$this->events_model->getParticipantsCompetetions($participant['events_details_participants_id'],$participant['events_details_events_id']);
						 $competitionNoDet=substr($competitionNos, 0, -2);
						/* echo "competition no det = " . $competitionNoDet;
						 exit;*/
					////// 	 $competitionNoDet=explode(",",$competitionNos);
						 $rowPosNo=$rowPosNo + 1;
						 $sheet->setCellValue('A'.$rowPosNo, $paymentDet['payment_date']);  
						 $sheet->setCellValue('B'.$rowPosNo,strtoupper($participant['participants_name'])); 
						 $sheet->setCellValue('C'.$rowPosNo,$participant['participants_city']); 
						 $sheet->setCellValue('V'.$rowPosNo,$participant['participants_city']); 
						 $sheet->setCellValue('D'.$rowPosNo,$participant['participants_phone']); 
						 $sheet->setCellValue('S'.$rowPosNo,$participant['events_details_name_of_tuner']);
						 $sheet->setCellValue('AA'.$rowPosNo,$participant['participants_blood_group']); 
						 $sheet->setCellValue('U'.$rowPosNo,$participant['participants_address']); 
					     $sheet->setCellValue('X'.$rowPosNo,$participant['participants_post']);   
						 $sheet->setCellValue('W'.$rowPosNo,$participant['participants_state']); 
						 $sheet->setCellValue('Y'.$rowPosNo,$participant['participants_email']); 
						 $sheet->setCellValue('Z'.$rowPosNo,$participant['participants_dob']); 
						 $sheet->setCellValue('H'.$rowPosNo,$participant['participants_civil_license_number']); 
						 $sheet->setCellValue('G'.$rowPosNo,strtoupper($participant['participants_fmsci_license_number'])); 
						 $sheet->setCellValue('AB'.$rowPosNo,$participant['participants_emergency_contact_person']); 
						 $sheet->setCellValue('AC'.$rowPosNo,(int)$participant['participants_emergency_contact_phone']); 
						 $sheet->setCellValue('U'.$rowPosNo,$participant['events_details_participants_team_name']); 
						 
						// collect category ids list and select one category and get the details of that category
						$eventVehicleDetails=$this->events_model->getEventDetVehicleDet($event_id, $participant['events_details_participants_id']);
						/*echo "<pre>";
						print_r($eventVehicleDetails);
						exit;*/
						 
						 $intCnt=0;
						 $compNoPos=-1;
						 foreach($eventVehicleDetails as $eventVehicle)
						 {
						    $intCnt=$intCnt+1;
							$compNoPos=$compNoPos+1;
						    $catDet=$this->events_model->getEventCategoryName($eventVehicle['events_details_category_id']);
						//	print_r($catDet);
							
							
							$category_name=$catDet['categories_name'];
						 	$sheet->setCellValue('F'.$rowPosNo, $category_name);    
							$sheet->setCellValue('N'.$rowPosNo, $eventVehicle['vehicle_details_number']);
							$sheet->setCellValue('P'.$rowPosNo, $eventVehicle['vehicle_model_name']);
							//  $sheet->setCellValue('O'.$rowPosNo,$eventVehicle['vehicle_details_cc']);    // not found
							$sheet->setCellValue('O'.$rowPosNo,$eventVehicle['vehicle_name']); 
							$sheet->setCellValue('Q'.$rowPosNo, $eventVehicle['vehicle_details_engine']);
					     	$sheet->setCellValue('R'.$rowPosNo, $eventVehicle['vehicle_details_chassis']);	
							
					 		$sheet->setCellValue('E'.$rowPosNo,$competitionNoDet);     ////////////////////////////////
					 
							
							
							 
							$rowPosNo=$rowPosNo + 1;
					     }
							$rowPosNo=$rowPosNo-$intCnt;
						
							$sheet->setCellValue('I'.$rowPosNo, $paymentDet['payment_amount']); 
							$sheet->setCellValue('M'.$rowPosNo, $paymentDet['payment_date']);
							$payment_id=$paymentDet['payment_id'];
							$upiPaymentDet=$this->events_model->getUpiPaymentDet($payment_id);
					//		echo "upi payment det = " . $upiPaymentDet;
							
							
							$sheet->setCellValue('J'.$rowPosNo, $upiPaymentDet['upi_details_transaction_id']);
							
							if($paymentDet['payment_status'] == 1)
							{
						  	   $paymentStatus="Confirm";
							}
							else if($paymentDet['payment_status'] == 0)
							{
						  	   $paymentStatus="Pending";
							}
							else
							{
							   $paymentStatus="Pending";
							}
							$sheet->setCellValue('K'.$rowPosNo, $paymentStatus);
							$sheet->setCellValue('L'.$rowPosNo, $upiPaymentDet['upi_details_comments']);
							
						
							$rowPosNo=$rowPosNo+($intCnt-1);
					
				}
			//	exit;
				
			foreach ($sheet->getColumnIterator() as $column) 
			{
   					$sheet->getColumnDimension($column->getColumnIndex())->setAutoSize(true);
			} 
				
				$writer = new Xlsx($spreadsheet);
				$filename = $events['events_name'];
				
				$writer->save($filename);  // will create and save the file in the root of the project
				
				
				header('Content-Type: application/vnd.ms-excel');
				header('Content-Disposition: attachment;filename="'. $filename .'.xlsx"');
				header('Cache-Control: max-age=0');
				$writer->save('php://output'); // download file
		 }
    } 
	
	
	
	public function testEmailResponsive()
	{
	 /*	echo "inside";
		
		$this->load->library('email');
		 
		$config['protocol'] = 'mail';
		$this->email->initialize($config);
		$this->email->set_newline("\r\n"); 
			
	
				$email_message="<!DOCTYPE html>
			<html lang='en' style='box-sizing: border-box;'>
			<head style='box-sizing: border-box;'>
			  <title style='box-sizing: border-box;'>Bootstrap Example</title>
			  <meta charset='utf-8' style='box-sizing: border-box;'>
			  <meta name='viewport' content='width=device-width, initial-scale=1' style='box-sizing: border-box;'>
			</head>
			
			
			<body style='margin: 0 auto;padding: 0px !important;text-align: center;font-family: 'Roboto';font-size: 17px;color: #231f20;box-sizing: border-box;font-weight: 400;line-height: 1.5;background-color: #fff;-webkit-text-size-adjust: 100%;-webkit-tap-highlight-color: transparent;'>
			  <div style='width: 950px;height: auto;background: #e5e5e5;text-align: center;margin: 0 auto;padding: 104px 0px 50px 0px;box-sizing: border-box;' class='outer'>
				  <div class='inner' style='width: 513px;height: auto;background: white;margin: 0 auto;border-radius: 15px;border-top: 8px solid #ff000a;padding: 25px 35px 25px 35px;box-sizing: border-box;'>
					  <img src='img/logo.jpg' alt='motoxindia.com' style='box-sizing: border-box;vertical-align: middle;'>
					  <p style='display: block;text-align: left;box-sizing: border-box;margin-top: 0;margin-bottom: 1rem;'>
						Dear Organizer, <br style='box-sizing: border-box;'>
						{Name } is registered the following event
					  </p>
					  <div class='container-fluid mb-12' style='box-sizing: border-box;width: 100%;padding-right: var(--bs-gutter-x,.75rem);padding-left: var(--bs-gutter-x,.75rem);margin-right: auto;margin-left: auto;margin-bottom: 12px;'>
						<div class='row' style='box-sizing: border-box;--bs-gutter-x: 1.5rem;--bs-gutter-y: 0;display: flex;flex-wrap: wrap;margin-top: calc(var(--bs-gutter-y) * -1);margin-right: calc(var(--bs-gutter-x) * -.5);margin-left: calc(var(--bs-gutter-x) * -.5);'>
						  <div class='col-sm-12 heading' style='box-sizing: border-box;flex-shrink: 0;width: 100%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;background: #e0e0e2;color: #fc333b;font-size: 18px;font-weight: 600;text-align: left;padding: 10px;'>
							  MRF MoGrip FMSCI National Supercross 2022 Round-3, Coimbatore
						  </div>
						</div>
						<div class='row text-style' style='box-sizing: border-box;--bs-gutter-x: 1.5rem;--bs-gutter-y: 0;display: flex;flex-wrap: wrap;margin-top: calc(var(--bs-gutter-y) * -1);margin-right: calc(var(--bs-gutter-x) * -.5);margin-left: calc(var(--bs-gutter-x) * -.5);background: #f5f5f7;color: #262525;font-size: 15px;text-align: left;margin-bottom: 1px;'>
						  <div class='col-sm-12 subheading' style='box-sizing: border-box;flex-shrink: 0;width: 100%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;background: #ecedef;color: #262525;font-size: 15px;font-weight: 500;text-align: left;padding: 10px 14px;'>
							  Personal Details
						  </div>
						</div>
						<div class='row text-style' style='box-sizing: border-box;--bs-gutter-x: 1.5rem;--bs-gutter-y: 0;display: flex;flex-wrap: wrap;margin-top: calc(var(--bs-gutter-y) * -1);margin-right: calc(var(--bs-gutter-x) * -.5);margin-left: calc(var(--bs-gutter-x) * -.5);background: #f5f5f7;color: #262525;font-size: 15px;text-align: left;margin-bottom: 1px;'>
						  <div class='col-sm-4 p10-15' style='box-sizing: border-box;flex-shrink: 0;width: 33.33333333%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;padding: 10px 15px;'>
							Full Name :
						  </div>
						  <div class='col-sm-8 p10-15' style='box-sizing: border-box;flex-shrink: 0;width: 66.66666667%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;padding: 10px 15px;'>
							Stephan Ronald
						  </div>
						</div>
						<div class='row text-style' style='box-sizing: border-box;--bs-gutter-x: 1.5rem;--bs-gutter-y: 0;display: flex;flex-wrap: wrap;margin-top: calc(var(--bs-gutter-y) * -1);margin-right: calc(var(--bs-gutter-x) * -.5);margin-left: calc(var(--bs-gutter-x) * -.5);background: #f5f5f7;color: #262525;font-size: 15px;text-align: left;margin-bottom: 1px;'>
						  <div class='col-sm-4 p10-15' style='box-sizing: border-box;flex-shrink: 0;width: 33.33333333%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;padding: 10px 15px;'>
							Address :
						  </div>
						  <div class='col-sm-8 p10-15' style='box-sizing: border-box;flex-shrink: 0;width: 66.66666667%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;padding: 10px 15px;'>
							Skyline Blue Heavan, Fl No.8, Tatapuram sukumaran road, Kacherippady, Ernakulam
						  </div>
						</div>
						<div class='row text-style' style='box-sizing: border-box;--bs-gutter-x: 1.5rem;--bs-gutter-y: 0;display: flex;flex-wrap: wrap;margin-top: calc(var(--bs-gutter-y) * -1);margin-right: calc(var(--bs-gutter-x) * -.5);margin-left: calc(var(--bs-gutter-x) * -.5);background: #f5f5f7;color: #262525;font-size: 15px;text-align: left;margin-bottom: 1px;'>
						  <div class='col-sm-4 p10-15' style='box-sizing: border-box;flex-shrink: 0;width: 33.33333333%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;padding: 10px 15px;'>
							City : Ernakulam
						  </div>
						  <div class='col-sm-4 p10-15' style='box-sizing: border-box;flex-shrink: 0;width: 33.33333333%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;padding: 10px 15px;'>
							State : Kerala
						  </div>
						  <div class='col-sm-4 p10-15' style='box-sizing: border-box;flex-shrink: 0;width: 33.33333333%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;padding: 10px 15px;'>
							PIN : 691554
						  </div>
						</div>
						<div class='row text-style' style='box-sizing: border-box;--bs-gutter-x: 1.5rem;--bs-gutter-y: 0;display: flex;flex-wrap: wrap;margin-top: calc(var(--bs-gutter-y) * -1);margin-right: calc(var(--bs-gutter-x) * -.5);margin-left: calc(var(--bs-gutter-x) * -.5);background: #f5f5f7;color: #262525;font-size: 15px;text-align: left;margin-bottom: 1px;'>
						  <div class='col-sm-6 p10-15' style='box-sizing: border-box;flex-shrink: 0;width: 50%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;padding: 10px 15px;'>
							Date of Birth : 12-03-1990
						  </div>
						  <div class='col-sm-6 p10-15' style='box-sizing: border-box;flex-shrink: 0;width: 50%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;padding: 10px 15px;'>
							Phone No : 0987564123
						  </div>
						</div>
						<div class='row text-style' style='box-sizing: border-box;--bs-gutter-x: 1.5rem;--bs-gutter-y: 0;display: flex;flex-wrap: wrap;margin-top: calc(var(--bs-gutter-y) * -1);margin-right: calc(var(--bs-gutter-x) * -.5);margin-left: calc(var(--bs-gutter-x) * -.5);background: #f5f5f7;color: #262525;font-size: 15px;text-align: left;margin-bottom: 1px;'>
						  <div class='col-sm-6 p10-15' style='box-sizing: border-box;flex-shrink: 0;width: 50%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;padding: 10px 15px;'>
							Email : <EMAIL>
						  </div>
						  <div class='col-sm-6 p10-15' style='box-sizing: border-box;flex-shrink: 0;width: 50%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;padding: 10px 15px;'>
							Blood Group : A+
						  </div>
						</div>
						<div class='row text-style' style='box-sizing: border-box;--bs-gutter-x: 1.5rem;--bs-gutter-y: 0;display: flex;flex-wrap: wrap;margin-top: calc(var(--bs-gutter-y) * -1);margin-right: calc(var(--bs-gutter-x) * -.5);margin-left: calc(var(--bs-gutter-x) * -.5);background: #f5f5f7;color: #262525;font-size: 15px;text-align: left;margin-bottom: 1px;'>
						  <div class='col-sm-6 p10-15' style='box-sizing: border-box;flex-shrink: 0;width: 50%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;padding: 10px 15px;'>
							Driving LIC No. : klxnh5622
						  </div>
						  <div class='col-sm-6 p10-15' style='box-sizing: border-box;flex-shrink: 0;width: 50%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;padding: 10px 15px;'>
							Valid Upto : 03-12-2025
						  </div>
						</div>
						<div class='row text-style' style='box-sizing: border-box;--bs-gutter-x: 1.5rem;--bs-gutter-y: 0;display: flex;flex-wrap: wrap;margin-top: calc(var(--bs-gutter-y) * -1);margin-right: calc(var(--bs-gutter-x) * -.5);margin-left: calc(var(--bs-gutter-x) * -.5);background: #f5f5f7;color: #262525;font-size: 15px;text-align: left;margin-bottom: 1px;'>
						  <div class='col-sm-6 p10-15' style='box-sizing: border-box;flex-shrink: 0;width: 50%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;padding: 10px 15px;'>
							FMSC LIC No. : klxnh5622
						  </div>
						  <div class='col-sm-6 p10-15' style='box-sizing: border-box;flex-shrink: 0;width: 50%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;padding: 10px 15px;'>
							Team : Team 1
						  </div>
						</div>
						<div class='row text-style' style='box-sizing: border-box;--bs-gutter-x: 1.5rem;--bs-gutter-y: 0;display: flex;flex-wrap: wrap;margin-top: calc(var(--bs-gutter-y) * -1);margin-right: calc(var(--bs-gutter-x) * -.5);margin-left: calc(var(--bs-gutter-x) * -.5);background: #f5f5f7;color: #262525;font-size: 15px;text-align: left;margin-bottom: 1px;'>
						  <div class='col-sm-12 p10-15' style='box-sizing: border-box;flex-shrink: 0;width: 100%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;padding: 10px 15px;'>
							Emergency Contact Person : George Kurian
						  </div>
						</div>
						<div class='row text-style' style='box-sizing: border-box;--bs-gutter-x: 1.5rem;--bs-gutter-y: 0;display: flex;flex-wrap: wrap;margin-top: calc(var(--bs-gutter-y) * -1);margin-right: calc(var(--bs-gutter-x) * -.5);margin-left: calc(var(--bs-gutter-x) * -.5);background: #f5f5f7;color: #262525;font-size: 15px;text-align: left;margin-bottom: 1px;'>
						  <div class='col-sm-12 p10-15' style='box-sizing: border-box;flex-shrink: 0;width: 100%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;padding: 10px 15px;'>
							Phone Number : 0123456789
						  </div>
						</div>
					  </div>
					  <div class='container-fluid' style='box-sizing: border-box;width: 100%;padding-right: var(--bs-gutter-x,.75rem);padding-left: var(--bs-gutter-x,.75rem);margin-right: auto;margin-left: auto;'>            
						<div class='row text-style' style='box-sizing: border-box;--bs-gutter-x: 1.5rem;--bs-gutter-y: 0;display: flex;flex-wrap: wrap;margin-top: calc(var(--bs-gutter-y) * -1);margin-right: calc(var(--bs-gutter-x) * -.5);margin-left: calc(var(--bs-gutter-x) * -.5);background: #f5f5f7;color: #262525;font-size: 15px;text-align: left;margin-bottom: 1px;'>
						  <div class='col-sm-12 subheading' style='box-sizing: border-box;flex-shrink: 0;width: 100%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;background: #ecedef;color: #262525;font-size: 15px;font-weight: 500;text-align: left;padding: 10px 14px;'>
							Vehicle Details
						  </div>
						</div>
						<div class='row text-style' style='box-sizing: border-box;--bs-gutter-x: 1.5rem;--bs-gutter-y: 0;display: flex;flex-wrap: wrap;margin-top: calc(var(--bs-gutter-y) * -1);margin-right: calc(var(--bs-gutter-x) * -.5);margin-left: calc(var(--bs-gutter-x) * -.5);background: #f5f5f7;color: #262525;font-size: 15px;text-align: left;margin-bottom: 1px;'>
						  <div class='col-sm-12 p10-15' style='box-sizing: border-box;flex-shrink: 0;width: 100%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;padding: 10px 15px;'>
							Classes to ride : Open class
						  </div>
						</div>
						<div class='row text-style' style='box-sizing: border-box;--bs-gutter-x: 1.5rem;--bs-gutter-y: 0;display: flex;flex-wrap: wrap;margin-top: calc(var(--bs-gutter-y) * -1);margin-right: calc(var(--bs-gutter-x) * -.5);margin-left: calc(var(--bs-gutter-x) * -.5);background: #f5f5f7;color: #262525;font-size: 15px;text-align: left;margin-bottom: 1px;'>
						  <div class='col-sm-6 p10-15' style='box-sizing: border-box;flex-shrink: 0;width: 50%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;padding: 10px 15px;'>
							Reg. No. : KL 03 25 3456
						  </div>
						  <div class='col-sm-6 p10-15' style='box-sizing: border-box;flex-shrink: 0;width: 50%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;padding: 10px 15px;'>
							Comp. No : 16
						  </div>
						</div>
						<div class='row text-style' style='box-sizing: border-box;--bs-gutter-x: 1.5rem;--bs-gutter-y: 0;display: flex;flex-wrap: wrap;margin-top: calc(var(--bs-gutter-y) * -1);margin-right: calc(var(--bs-gutter-x) * -.5);margin-left: calc(var(--bs-gutter-x) * -.5);background: #f5f5f7;color: #262525;font-size: 15px;text-align: left;margin-bottom: 1px;'>
						  <div class='col-sm-6 p10-15' style='box-sizing: border-box;flex-shrink: 0;width: 50%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;padding: 10px 15px;'>
							Make : Honda
						  </div>
						  <div class='col-sm-6 p10-15' style='box-sizing: border-box;flex-shrink: 0;width: 50%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;padding: 10px 15px;'>
							Modal : RX 100
						  </div>
						</div>
						<div class='row text-style' style='box-sizing: border-box;--bs-gutter-x: 1.5rem;--bs-gutter-y: 0;display: flex;flex-wrap: wrap;margin-top: calc(var(--bs-gutter-y) * -1);margin-right: calc(var(--bs-gutter-x) * -.5);margin-left: calc(var(--bs-gutter-x) * -.5);background: #f5f5f7;color: #262525;font-size: 15px;text-align: left;margin-bottom: 1px;'>
						  <div class='col-sm-6 p10-15' style='box-sizing: border-box;flex-shrink: 0;width: 50%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;padding: 10px 15px;'>
							Chassis No. : 2564lkys45
						  </div>
						  <div class='col-sm-6 p10-15' style='box-sizing: border-box;flex-shrink: 0;width: 50%;max-width: 100%;padding-right: calc(var(--bs-gutter-x) * .5);padding-left: calc(var(--bs-gutter-x) * .5);margin-top: var(--bs-gutter-y);flex: 0 0 auto;padding: 10px 15px;'>
							Engine No. : RX 100537hg
						  </div>
						</div>
					  </div>
					  <table style='width: 100%;text-align: center;margin-top: 10px;box-sizing: border-box;caption-side: bottom;border-collapse: collapse;'>
						<tr style='background: #f5f5f7;color: #262525;font-size: 15px;box-sizing: border-box;border-color: inherit;border-style: solid;border-width: 0;'>
						  <td colspan='3' style='padding: 10px 15px;width: 50%;box-sizing: border-box;border-color: inherit;border-style: solid;border-width: 0;'>
							<p style='color: #262525;font-size: 15px;font-weight: 500;box-sizing: border-box;margin-top: 0;margin-bottom: 1rem;'>Quick Contact</p>
							<div style='padding-bottom: 30px;box-sizing: border-box;'>
							  <img src='img/wtsapp.jpg' alt='' style='margin: 0px 15px;box-sizing: border-box;vertical-align: middle;'>
							  <img src='img/ph.jpg' alt='' style='margin: 0px 15px;box-sizing: border-box;vertical-align: middle;'>
							  <img src='img/mail.jpg' alt='' style='margin: 0px 15px;box-sizing: border-box;vertical-align: middle;'>
							</div>
						  </td>
						</tr>
					  </table>
					  
				  </div> 
				  <a href='javascript:void(0);' style='background: #001efd;color: white;text-decoration: none;font-size: 14px;padding: 10px 50px;border-radius: 50px;margin-top: 35px;display: inline-block;box-sizing: border-box;'>To Confirm this Entry Click Here</a>
			
				  <div style='color: #231f20;font-size: 15px;margin-top: 30px;box-sizing: border-box;'>
					  2023 motoxindia.com
				  </div>
			  </div>  
			</body>
			</html>";
	
	
	
	
	
	
// echo "email message = " . $email_message;
// exit;
	 
	 					$result = $this->email
						->from('<EMAIL>','MotoXindia')
						->reply_to('<EMAIL>')    // Optional, an account where a human being reads.
						->to("<EMAIL>,$organizer_email,<EMAIL>,<EMAIL>")
					//	->to('<EMAIL>')
					//	->to('<EMAIL>')
						->subject('Responsive Email')
						->message($email_message)
						->send();
	 					echo "result = " . $result;*/
						
						
						
						
	}   
	
	
	
	 public function trainingProgramEvents()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect("login");
        }
        else
        {
            $data['events'] = $this->events_model->getTrainingProgramEvents();
            $this->template->write('title', 'All Training Program Events', TRUE);
            $this->template->add_js('assets/vendors/js/tables/jquery.dataTables.min.js');
            $this->template->add_js('assets/vendors/js/tables/datatable/dataTables.bootstrap4.min.js');
            $this->template->add_js('assets/js/scripts/tables/datatables/datatable-basic.js');
            $this->template->add_css('assets/vendors/css/tables/datatable/dataTables.bootstrap4.min.css');
            $this->template->write_view('content', 'templates/pages/Events/list-training-program-events', $data, TRUE);
            $this->template->render();
        }
    }
	
	
	public function addTrainingProgramEvents()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $data['title'] = 'Add New Training Program Events';
            $data['participants'] = 'hello';
            
            //$data['languages'] = $this->events_model->getLanguages();
    
            $this->template->write('title', 'Add Training Program Events', TRUE);
            
            $this->form_validation->set_rules('events_name', 'Event Name', 'required');
            $this->form_validation->set_rules('events_venue', 'Event Venue', 'required');
            //$this->form_validation->set_rules('hour', 'Valid Time', 'required');
    
    
          
            if ($this->form_validation->run() == false) {
                $this->session->set_flashdata('addEventsError', 'Error in adding events');
                $this->template->write_view('content', 'templates/pages/Events/add-training-program-events', $data, TRUE);
            }
            else {
                $addEventsStatus = $this->events_model->addTrainingProgramEvents();
                if ($addEventsStatus) {
                    $this->session->set_flashdata('addTrainingProgramEvents', 'Events Added');
                    redirect(base_url('events/training-program-events'));
                } else {
                    $this->session->set_flashdata('addEventsError', 'Error in adding events');
                    $this->template->write_view('content', 'templates/pages/Events/add-training-program-events', $data, TRUE);
                }
            }
    
          /*  $this->template->add_js('assets/vendors/js/dropzone/dropzone.min.js');
            $this->template->add_js('assets/vendors/js/dropzoneFileUpload.js');
    
            $this->template->add_css('assets/vendors/css/dropzone/dropzone.min.css'); */
    
            $this->template->write_view('content', 'templates/pages/Events/add-training-program-events', $data, TRUE);
            $this->template->render();
        }
    }
	
	public function addToTrainingProgramEvents($id)
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
			$data['title'] = 'Add To Training Program Events';
            $data['events_id']=$id;
           
		    if($this->session->userdata('user_type')==2) 
			{
             	$this->confirmTrainingProgramParticipants($this->session->userdata('user_id'),$id); 
            } 
			else
            {
				$data['participants'] = $this->events_model->getAllParticipants();
				$data['events'] = $this->events_model->getSingleTrainingProgramEvents($id); 
			

				$this->template->write('title', 'Add To Training Program Events', TRUE); 
				$this->template->add_js('assets/vendors/js/tables/jquery.dataTables.min.js');
				$this->template->add_js('assets/vendors/js/tables/datatable/dataTables.bootstrap4.min.js');
				$this->template->add_js('assets/js/scripts/tables/datatables/datatable-basic.js');
				$this->template->add_css('assets/vendors/css/tables/datatable/dataTables.bootstrap4.min.css');
				$this->template->write_view('content', 'templates/pages/Events/training-program-list-participants', $data, TRUE);
				$this->template->render();
            }
        }
    }    
	public function confirmTrainingProgramParticipants($p_id,$e_id)
    { 
		if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $data['trainingProgramEventParticipantsSidebarList']=$this->adds_model->addsList(15);
			$data['trainingProgramEventOrganizerSidebarList']=$this->adds_model->addsList(16);
		    $data['title'] = 'Confirm Training Program Participants';
            $data['events_id']=$e_id;
            $data['participants_id']=$p_id;
            $participantsCode = array();
            $data['participants'] = $this->events_model->getSingleParticipants($p_id);
			$data['eventType'] = "Training Program";
			
			$data['eventParticipants'] = $this->events_model->getAllParticipantsbyTrainingProgramEventSuccessPayment($e_id);
			
			$data['eventPendingParticipants'] = $this->events_model->getAllParticipantsbyTrainingProgramEventPendingPayment($e_id);
			
            $data['orderedCategoryLists'] = $this->events_model->getTypeOrderedTrainingProgramCategoryList($e_id);
			
            $participantsCode=$this->events_model->getAllTrainingProgramParticipantsCode($e_id); 
			
            if($this->input->post('removeFromEvent') == 1)
            {
                $this->removeParticipantsFromTrainingProgramEvents($p_id,$e_id);
            }
			
            for($i=1;$i<=100;$i++)
            {
                $a[$i-1]=$i;
            }
            if($participantsCode == NULL )
            {
                $participantsCode['participants_code']= array();           
            }
			
			$data['participants_code'] = $participantsCode;
			 
            $data['participants_bill']=$this->events_model->getTrainingProgramParticipantsBill($p_id,$e_id);
			
            $data['events'] = $this->events_model->getSingleTrainingProgramEvents($e_id);
			$data['orderedCategoryLists'] = $this->events_model->getTypeOrderedTrainingProgramCategoryList($e_id);
            if($data['events']==NULL)
            $data['events']['categories']= array();
            $data['participants_categories'] = [];	
            $data['event_status'] = 0;
            if(empty($data['participants_categories']))
                $data['confirm_status'] = 0;
            else
            {
                $data['confirm_status'] = 1;
                $data['payments'] = $this->events_model->getTrainingProgramPaymantsdetails($p_id,$e_id);
                $data['event_status'] = $data['participants_categories']['events_details']['status'];
            }
            $this->template->write('title', 'Confirm Participants', TRUE);  
			$this->template->add_css('assets/css/glyphicon.css');
            $this->template->add_css('assets/css/seat.css');
            $this->template->add_css('assets/css/owl.carousel.css');
            $this->template->add_css('assets/css/owl.theme.default.css');
            $this->template->add_css('assets/css/style2.css');
            $this->template->add_css('assets/css/select2.min.css');        
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
            $this->template->add_js('assets/js/vendors/owl.carousel.js');
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');
            $this->template->add_js('assets/js/participant-list.js');
            $this->template->add_js('assets/js/select2.min.js');
            $this->template->write_view('content', 'templates/pages/Events/confirm-training-program-participants', $data, TRUE);
            $this->template->render();
        }
    }
	
	
	public function removeParticipantsFromTrainingProgramEvents($p_id,$e_id)
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $removeEventsStatus = $this->events_model->removeParticipantsFromTrainingProgramEvents($p_id,$e_id);
            if ($removeEventsStatus) {
                $this->session->set_flashdata('removeFromEvents', 'Participats removed from events');
                    redirect(base_url('events'));
            } else {
                $this->session->set_flashdata('removeFromEventsError', 'Error in participats removing from events');
                    redirect(base_url('events'));
            }            
        }
    } 
	
	
	public function removeParticipantsFromTicketmEvents($p_id,$e_id)
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $removeEventsStatus = $this->events_model->removeParticipantsFromTicketEvents($p_id,$e_id);
            if ($removeEventsStatus) {
                $this->session->set_flashdata('removeFromEvents', 'Participats removed from events');
                    redirect(base_url('events'));
            } else {
                $this->session->set_flashdata('removeFromEventsError', 'Error in participats removing from events');
                    redirect(base_url('events'));
            }            
        }
    } 
	
	
	
	public function addParticipantsToTrainingProgramEvents()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
		
            $this->form_validation->set_rules('events_id', 'Event Id', 'required');
            $this->form_validation->set_rules('participants_id', 'Participants Id', 'required');
            $this->form_validation->set_rules('category_cnt', 'Category', 'required');
					
            
            if ($this->form_validation->run() == false)
			{
                $this->session->set_flashdata('confirmEventsError', 'Error in confirming events');
                redirect('events/training-program-events');
            }
            else {
			
                    if($this->input->post('removeFromEvent')== 1)
					{
                       // $addEventsStatus = $this->events_model->removeCarParticipantsFromEvents($this->input->post('participants_id'),$this->input->post('events_id'));
					}
                    else
					{
						$addEventsStatus = $this->events_model->addParticipantsToTrainingProgramEvents();
					}
					

                    $data['events'] = $this->events_model->getSingleTrainingProgramEvents($this->input->post('events_id'));
					
					$eventsDet = $this->events_model->getSingleTrainingProgramEvents($this->input->post('events_id'));
					
				
                    
					
					if($eventsDet)
					{
							if($eventsDet['api_key'] != "")
							{
							   $api_key=$eventsDet['api_key'];
							}
							else
							{
							   $api_key=$this->api_key;
							}
							if($eventsDet['auth_token'] != "")
							{
							   $auth_token=$eventsDet['auth_token'];
							}
							else
							{
							   $auth_token=$this->auth_token;
							}
					}
					else
					{
							$api_key=$this->api_key;
							$auth_token=$this->auth_token;
					}
				
				
					
                    if($this->session->userdata('user_type')!=2)
                    {
                        if($this->input->post('print_status')==1)
                        {
                            redirect('participants/training-program-event-entry-form-detailed-shortcut/'.$this->input->post('participants_id').'/'.$this->input->post('events_id'));
                        }
                        else
                        {
                            if ($addEventsStatus) {
                                if($this->input->post('removeFromEvent')== 1)
                                    $this->session->set_flashdata('confirmEvents', 'Removed from Event');
                                else
                                $this->session->set_flashdata('confirmEvents', 'Events Confirmed');
                                redirect(base_url('participants'));
                            } else {
                                    if($this->input->post('removeFromEvent')== 1)
                                        $this->session->set_flashdata('confirmEventsError', 'Error in removing from events');
                                    else
                                        $this->session->set_flashdata('confirmEventsError', 'Error in confirming events');
                                    redirect(base_url('participants'));
                            }
                        }
                    }
                    else
                    {
					    $statusArray = explode('$#',$addEventsStatus);
                        if($statusArray[1]==0)
                        {
                            redirect('events/training-program-payment-confirm/'.$this->input->post('events_id'));
                        }
						
						
                        $dataPay['title'] = 'Proceed with Payment mode ';
                        $dataPay['payment_mode'] = $this->input->post('payment_mode');
                        $dataPay['payments_dtl']=array(
                        "purpose" => urlencode($statusArray[0]),
                        "amount" => $statusArray[1],
                        "send_email" => true, // changed to false regi after uploading changed it into true
                        "email" => $this->input->post('participants_email'),
                        'phone' => $this->input->post('phone'),
                        'buyer_name' => $this->input->post('buyer_name'),     
                        'events_id'=> $this->input->post('events_id'),     
						'api_key' => $api_key,
						'auth_token' =>  $auth_token,                  
                        "redirect_url" => base_url('events/do-after-training-program-payment')                       
                        );

                        // store data to flashdata
                        $this->session->set_flashdata('paymentData',$dataPay);
                        // after storing redirect it to the controller
                        redirect('instamojo/insta-mojo-create');
						//---------------
						
						
                                        
                    }
                }
        }
        
    }   
	
	
	public function trainingProgramPaymentConfirm($e_id)
    {
        if($this->session->userdata('user_id') == NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
			$data['paymentConfirm']=$this->adds_model->addsList(30);
            $data['title'] = 'Payment Confirm';
            $p_id = $this->session->userdata('user_id');
            $data['eventPaymentRegInfo'] = $this->events_model->getTrainingProgramEventPaymentRegInfo($e_id,$p_id);
			$data['eventParticipants'] = $this->events_model->getAllParticipantsbyTrainingProgramEventSuccessPayment($e_id);
			$data['eventPendingParticipants'] = $this->events_model->getAllParticipantsbyTrainingProgramEventPendingPayment($e_id);
			$data['events'] = $this->events_model->getSingleTrainingProgramEvents($e_id);
            $data['eventType'] = "Training Program";
            // print_r($data['eventPaymentRegInfo']);exit;
            $this->template->write('title', 'Payment Confirm', TRUE);  
            $this->template->add_css('assets/css/style2.css');          
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js'); 
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');
			

			$data['participants_id']=$p_id;
			$data['participants_events_id']=$e_id;
			$data['events_id']=$e_id;

            $this->template->write_view('content', 'templates/pages/Events/payment-confirm', $data, TRUE);


            $this->template->render();
        }
    }
 
    public function trainingProgramPaymentProcess($e_id)
    {
        if($this->session->userdata('user_id') == NULL)
        {
            redirect(base_url('login'));
            
        }
        else
        {
			$data['trainingprogramPaymentProcess']=$this->adds_model->addsList(27);
            $this->form_validation->set_rules('upiTransactionId', 'UPI Transaction Id', 'required');
            //$this->form_validation->set_rules('category_cnt', 'Category Count', 'required');
            $data['e_id'] = $e_id;
            $p_id = $this->session->userdata('user_id');
            $data['checkPaymentWithUPI'] = $this->events_model->checkTrainingProgramPaymentWithUPI($e_id,$p_id);
            $data['events'] = $this->events_model->getSingleTrainingProgramEvents($e_id);
            $data['eventPaymentRegInfo'] = $this->events_model->getTrainingProgramEventPaymentRegInfo($e_id,$p_id);
            $data['eventParticipants'] = $this->events_model->getAllParticipantsbyTrainingProgramEventSuccessPayment($e_id);
			$data['eventPendingParticipants'] = $this->events_model->getAllParticipantsbyTrainingProgramEventPendingPayment($e_id);
			
			
			$data['participants_id']=$this->session->userdata('user_id');
			$data['event_entry_details'] = $this->events_model->getTrainingProgramEventEntryDetails($data['participants_id'],$e_id);
			
			

            if ($this->form_validation->run() == false) {

                $this->session->set_flashdata('upiTransactionError', 'Error in UPI Transaction Id submission.Please fill the mandatory fields.');
            }
            else {           
                $data['updatePaymentWithUPI'] = $this->events_model->updateTrainingProgramPaymentWithUPI();
                $path = 'events/training-program-payment-process/'.$e_id;
                redirect(base_url($path));
            }
            $data['title'] = 'Payment Process';
            $this->template->write('title', 'Payment Process', TRUE);  
            $this->template->add_css('assets/css/style2.css');
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');
            $this->template->write_view('content', 'templates/pages/Events/training-program-payment-process', $data, TRUE);
            $this->template->render();
       
        }
    }
	
	
	public function doAfterTrainingProgramPayment()
    {
        //print_r($response);exit;
        
        //if($_GET['payment_request_id']!='')
	
		$eventsDet = $this->events_model->getSingleTrainingProgramEvents($_GET['events_id']);
		$data['paymentConfirm']=$this->adds_model->addsList(30);
		
		if($eventsDet)
		{
				if($eventsDet['api_key'] != "")
				{
				   $api_key=$eventsDet['api_key'];
				}
				else
				{
				   $api_key=$this->api_key;
				}
				if($eventsDet['auth_token'] != "")
				{
				   $auth_token=$eventsDet['auth_token'];
				}
				else
				{
				   $auth_token=$this->auth_token;
				}
		}
		else
		{
				$api_key=$this->api_key;
				$auth_token=$this->auth_token;
		}
			
        if(isset($_GET['payment_id'])!='' && isset($_GET['payment_request_id'])!='')
        {
        

            $ch = curl_init();
            $url = $this->endpoint.$_GET['payment_request_id'];
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HEADER, FALSE);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, TRUE);
            curl_setopt($ch, CURLOPT_HTTPHEADER,
                        array("X-Api-Key:".$api_key,
                              "X-Auth-Token:".$auth_token));
            $response = curl_exec($ch);
            curl_close($ch);

        $result = json_decode($response);//$response$this->paymentRequestStatus($_GET['payment_request_id']);
        sleep(2);
        //print_r($result);
        // "Payment Successully Done!";
        //$data['payment_requests']=$_GET['payment_requests'];
            if($_GET['payment_status']!='Failed'){
            $data['payment_requests']['success']=$result->payment_request->payments[0]->status;
            $data['payment_requests']['mode']='online';
            $data['payment_requests']['trans_id']=$result->payment_request->id;
            $data['payment_requests']['payment_id']=$result->payment_request->payments[0]->payment_id;
            $data['payment_requests']['purpose']=urldecode($result->payment_request->purpose);
            }
            else{
                $data['payment_requests']['success']='Pending';
                $data['payment_requests']['mode']='online';
                $data['payment_requests']['trans_id']=$_GET['payment_request_id'];
                $data['payment_requests']['payment_id']=$_GET['payment_id'];
                $data['payment_requests']['purpose']=urldecode($result->payment_request->purpose);
                }
        }   
        else if(isset($_GET['mode'])!='' && isset($_GET['purpose'])!='')
        {

        $data['payment_requests']['success']='Pending';
        $data['payment_requests']['mode']=$_GET['mode'];
        $data['payment_requests']['trans_id']='-';
        $data['payment_requests']['payment_id']='-';
        $data['payment_requests']['purpose']=urldecode($_GET['purpose']);
        $data['payment_requests']['events_id']=$_GET['events_id'];
        $data['payment_requests']['user_id']=$this->session->userdata('user_id');
        }
        else
        {
            show_404();

        }
            $payment_update_dtls=$this->events_model->trainingProgramPaymentUpdate($data['payment_requests']);
		
            $data['title'] = 'Payment '.$data['payment_requests']['success'].'!';
            $e_id=$payment_update_dtls[0]['payment_event_id'];
            $data['events_id']=$e_id;
            $data['participants_id']=$this->session->userdata('user_id');
            $data['participants'] = $this->events_model->getSingleParticipants($this->session->userdata('user_id'));
            $data['events'] = $this->events_model->getSingleTrainingProgramEvents($e_id);
			
			
			
			$p_id = $this->session->userdata('user_id');
		    $data['eventPaymentRegInfo'] = $this->events_model->getTrainingProgramEventPaymentRegInfo($e_id,$p_id);
			
				
            $data['payment_status'] = $data['payment_requests']['success'];
            $data['eventParticipants'] = $this->events_model->getAllParticipantsbyTrainingProgramEventSuccessPayment($e_id);
 			// $data['checkPaymentWithUPI'] = $this->events_model->checkPaymentWithUPI($e_id,$p_id);
			
			////////////////////////////////////////////////////////////////////
			$data['e_id'] = $e_id;
        //    $p_id = $this->session->userdata('user_id');
            $data['checkPaymentWithUPI'] = $this->events_model->checkTrainingProgramPaymentWithUPI($e_id,$p_id);
			
		
			/////////////////////////////////////////////////////////////////////
 
            // if($data['events']==NULL)
            // $data['events']['categories']= array();
            $data['participants_categories'] = $this->events_model->getTrainingProgramParticipantsCategories($this->session->userdata('user_id'),$e_id);
            $data['confirm_status'] = 1;
            $data['payments'] = $this->events_model->getTrainingProgramPaymantsdetails($this->session->userdata('user_id'),$e_id);
				
            $data['event_status'] = $data['participants_categories']['events_details']['status'];
            $this->template->write('title', 'Payment '.$data['payment_requests']['success'].'!', TRUE);  
            $data['eventPaymentRegInfo'] = $this->events_model->getTrainingProgramEventPaymentRegInfo($e_id,$this->session->userdata('user_id'));

            $this->template->add_css('assets/css/style2.css');
            $this->template->add_css('assets/css/select2.min.css');
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');

            //$this->template->add_js('assets/js/bootstrap-select.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');   
            $this->template->add_js('assets/js/select2.min.js');
			
			$data['eventType']="Training Program";
            if(isset($_GET['mode'])!='' && isset($_GET['purpose'])!=''){
			
                $data['event_entry_details'] = $this->events_model->getTrainingProgramEventEntryDetails($data['participants_id'],$e_id);
                //print_r($data['event_entry_details']);
                if($data['event_entry_details'][0]['upi_details_transaction_id'] == '')
				{
				    $data['advtDetails']=$this->adds_model->addsList(32);
                    $this->template->write_view('content', 'templates/pages/Events/training-program-upi-payment', $data, TRUE);
				}
                else
				{
					$data['carPaymentProcess']=$this->adds_model->addsList(27);
                    $this->template->write_view('content', 'templates/pages/Events/training-program-payment-process', $data, TRUE);
				}
            }
            else
				{ 
					$data['advtDetails']=$this->adds_model->addsList(34);
              		$this->template->write_view('content', 'templates/pages/Events/payment-confirm', $data, TRUE);
				}
            $this->template->render();            

    } 
	
	
	public function addToTicketEvents($id)
    {
       if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
			$data['title'] = 'Add To Ticket Events';
           $data['events_id']=$id;
           
		    if($this->session->userdata('user_type')==2) 
			{
             	$this->confirmTicketParticipants($this->session->userdata('user_id'),$id); 
            } 
			else
            {
				$data['participants'] = $this->events_model->getAllParticipants();
				$data['events'] = $this->events_model->getSingleTicketEvents($id); 
			
  
				$this->template->write('title', 'Add To Ticket Events', TRUE); 
				$this->template->add_js('assets/vendors/js/tables/jquery.dataTables.min.js');
				$this->template->add_js('assets/vendors/js/tables/datatable/dataTables.bootstrap4.min.js');
				$this->template->add_js('assets/js/scripts/tables/datatables/datatable-basic.js');
				$this->template->add_css('assets/vendors/css/tables/datatable/dataTables.bootstrap4.min.css');
				$this->template->write_view('content', 'templates/pages/Events/ticket-program-list-participants', $data, TRUE);
				$this->template->render();
            }
        }
    }  
	
	
	public function confirmTicketParticipants($p_id,$e_id)
    { 
		if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
            $data['ticketEventParticipantsSidebarList']=$this->adds_model->addsList(15);
			$data['ticketEventOrganizerSidebarList']=$this->adds_model->addsList(16);
		    $data['title'] = 'Confirm Ticket Event Participants';
            $data['events_id']=$e_id;
            $data['participants_id']=$p_id;
            $participantsCode = array();
            $data['participants'] = $this->events_model->getSingleParticipants($p_id);
			$data['eventType'] = "Ticket Event";
			
			$data['eventParticipants'] = $this->events_model->getAllParticipantsbyTicketEventSuccessPayment($e_id);
			
			$data['eventPendingParticipants'] = $this->events_model->getAllParticipantsbyTicketEventPendingPayment($e_id);
			
			
            $data['orderedCategoryLists'] = $this->events_model->getTypeOrderedTicketCategoryList($e_id);
			
       //     $participantsCode=$this->events_model->getAllTicketParticipantsCode($e_id); 
			
            if($this->input->post('removeFromEvent') == 1)
            {
                $this->removeParticipantsFromTicketmEvents($p_id,$e_id);
            }
			
            for($i=1;$i<=100;$i++)
            {
                $a[$i-1]=$i;
            }
            if($participantsCode == NULL )
            {
                $participantsCode['participants_code']= array();           
            }
			
			$data['participants_code'] = $participantsCode;
			 
            $data['participants_bill']=$this->events_model->getTicketParticipantsBill($p_id,$e_id);
			
            $data['events'] = $this->events_model->getSingleTicketEvents($e_id);
			$data['orderedCategoryLists'] = $this->events_model->getTypeOrderedTicketCategoryList($e_id);
            if($data['events']==NULL)
            $data['events']['categories']= array();
            $data['participants_categories'] = [];	
            $data['event_status'] = 0;
            if(empty($data['participants_categories']))
                $data['confirm_status'] = 0;
            else
            {
                $data['confirm_status'] = 1;
                $data['payments'] = $this->events_model->getTicketPaymantsdetails($p_id,$e_id);
                $data['event_status'] = $data['participants_categories']['events_details']['status'];
            }
            $this->template->write('title', 'Confirm Participants', TRUE);  
			$this->template->add_css('assets/css/glyphicon.css');
            $this->template->add_css('assets/css/seat.css');
            $this->template->add_css('assets/css/owl.carousel.css');
            $this->template->add_css('assets/css/owl.theme.default.css');
            $this->template->add_css('assets/css/style2.css');
            $this->template->add_css('assets/css/select2.min.css');        
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
            $this->template->add_js('assets/js/vendors/owl.carousel.js');
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');
            $this->template->add_js('assets/js/participant-list.js');
            $this->template->add_js('assets/js/select2.min.js');
            $this->template->write_view('content', 'templates/pages/Events/confirm-ticket-participants', $data, TRUE);
            $this->template->render();
        }
    }  
	
	
	public function addParticipantsToTicketEvents()
    {
        if($this->session->userdata('user_id')==NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
		
           /* $this->form_validation->set_rules('events_id', 'Event Id', 'required');
            $this->form_validation->set_rules('participants_id', 'Participants Id', 'required');
            $this->form_validation->set_rules('category_cnt', 'Category', 'required');
					
            
            if ($this->form_validation->run() == false)
			{
               $this->session->set_flashdata('confirmEventsError', 'Error in confirming events');
			 //  echo validation_errors();
			 //  exit;
             //  redirect('events/training-program-ticket-events');
            }
            else {*/
					//echo "<pre>";
					
				//	print_r($data['attendeeDetails']);
				//	exit;
			
                    if($this->input->post('removeFromEvent')== 1)
					{
                        $addEventsStatus = $this->events_model->removeParticipantsFromTicketEvents($this->input->post('participants_id'),$this->input->post('events_id'));
					}
                    else
					{
						$addEventsStatus = $this->events_model->addParticipantsToTicketEvents();
					}
					
				
					

                    $data['events'] = $this->events_model->getSingleTicketEvents($this->input->post('events_id'));
					
					$eventsDet = $this->events_model->getSingleTicketEvents($this->input->post('events_id'));
					
				
                    
					
					if($eventsDet)
					{
							if($eventsDet['api_key'] != "")
							{
							   $api_key=$eventsDet['api_key'];
							}
							else
							{
							   $api_key=$this->api_key;
							}
							if($eventsDet['auth_token'] != "")
							{
							   $auth_token=$eventsDet['auth_token'];
							}
							else
							{
							   $auth_token=$this->auth_token;
							}
					}
					else
					{
							$api_key=$this->api_key;
							$auth_token=$this->auth_token;
					}
				
				
					
                    if($this->session->userdata('user_type')!=2)
                    {
                       if($this->input->post('print_status')==1)
                        {
                            redirect('participants/ticket-event-entry-form-detailed-shortcut/'.$this->input->post('participants_id').'/'.$this->input->post('events_id'));
                        }
                        else
                        {
                            if ($addEventsStatus) {
                                if($this->input->post('removeFromEvent')== 1)
                                    $this->session->set_flashdata('confirmEvents', 'Removed from Event');
                                else
                                $this->session->set_flashdata('confirmEvents', 'Events Confirmed');
                                redirect(base_url('participants'));
                            } else {
                                    if($this->input->post('removeFromEvent')== 1)
                                        $this->session->set_flashdata('confirmEventsError', 'Error in removing from events');
                                    else
                                        $this->session->set_flashdata('confirmEventsError', 'Error in confirming events');
                                    redirect(base_url('participants'));
                            }
                        } 
                    }
                    else
                    {
					    $statusArray = explode('$#',$addEventsStatus);
					//	echo "<pre>";
						
				//		print_r($statusArray[1]);
						
					//	exit;
                        if($statusArray[1]==0)
                        {
                            redirect('events/ticket-payment-confirm/'.$this->input->post('events_id'));
                        }
						
						
                        $dataPay['title'] = 'Proceed with Payment mode ';
                        $dataPay['payment_mode'] = $this->input->post('payment_mode');
                        $dataPay['payments_dtl']=array(
                        "purpose" => urlencode($statusArray[0]),
                        "amount" => $statusArray[1],
                        "send_email" => true, // changed to false regi after uploading changed it into true
                        "email" => $this->input->post('participants_email'),
                        'phone' => $this->input->post('phone'),
                        'buyer_name' => $this->input->post('buyer_name'),     
                        'events_id'=> $this->input->post('events_id'),     
						'api_key' => $api_key,
						'auth_token' =>  $auth_token,                  
                        "redirect_url" => base_url('events/do-after-ticket-payment')                       
                        );

                        // store data to flashdata
                        $this->session->set_flashdata('paymentData',$dataPay);
                        // after storing redirect it to the controller
                        redirect('instamojo/insta-mojo-create');
						//---------------
						
						
                                        
                    }  
           //    } 
      
		}
        
    }  
	
	
	public function ticketPaymentConfirm($e_id)
    {
        if($this->session->userdata('user_id') == NULL)
        {
            redirect(base_url('login'));
        }
        else
        {
			$data['paymentConfirm']=$this->adds_model->addsList(30);
            $data['title'] = 'Payment Confirm';
            $p_id = $this->session->userdata('user_id');
            $data['eventPaymentRegInfo'] = $this->events_model->getTicketEventPaymentRegInfo($e_id,$p_id);
			$data['eventParticipants'] = $this->events_model->getAllParticipantsbyTicketEventSuccessPayment($e_id);
			$data['eventPendingParticipants'] = $this->events_model->getAllParticipantsbyTicketEventPendingPayment($e_id);
			$data['eventType'] = "Ticket Events";
			$data['events'] = $this->events_model->getSingleTicketEvents($e_id);
			
			$eventsOrganizer=$data['events'];
			$eventsOrganizerId=$eventsOrganizer['events_organizer'];
			
			$organizerBikeEventDetails=$this->events_model->getAllEventsByOrganizer($eventsOrganizerId);
			$organizerBikeRallyEventDetails=$this->events_model->getAllBikeRallyEventsByOrganizer($eventsOrganizerId);
			$organizerCarEventDetails=$this->events_model->getAllCarEventsByOrganizer($eventsOrganizerId);
			
			
			$data['organizerEventDetails']=array_merge($organizerBikeEventDetails,$organizerBikeRallyEventDetails,$organizerCarEventDetails);
			$dates = array();
			foreach ($data['organizerEventDetails'] as $key => $row) {
				$dates[$key] = strtotime($row['events_date']);
			}
			// Sort $data based on dates in descending order
			array_multisort($dates, SORT_DESC, $data['organizerEventDetails']);
			
			
			
			
			$data['events_id']=$e_id;
            
            // print_r($data['eventPaymentRegInfo']);exit;
            $this->template->write('title', 'Payment Confirm', TRUE);  
            $this->template->add_css('assets/css/style2.css');          
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js'); 
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');
			

			$data['participants_id']=$p_id;
			$data['participants_events_id']=$e_id;

            $this->template->write_view('content', 'templates/pages/Events/ticket-payment-confirm', $data, TRUE);


            $this->template->render();
        }
    }
	
	
	
	public function doAfterTicketPayment()
    {
        //print_r($response);exit;
        
        //if($_GET['payment_request_id']!='')
	
		$eventsDet = $this->events_model->getSingleTicketEvents($_GET['events_id']);
		$data['paymentConfirm']=$this->adds_model->addsList(30);
		
		if($eventsDet)
		{
				if($eventsDet['api_key'] != "")
				{
				   $api_key=$eventsDet['api_key'];
				}
				else
				{
				   $api_key=$this->api_key;
				}
				if($eventsDet['auth_token'] != "")
				{
				   $auth_token=$eventsDet['auth_token'];
				}
				else
				{
				   $auth_token=$this->auth_token;
				}
		}
		else
		{
				$api_key=$this->api_key;
				$auth_token=$this->auth_token;
		}
			
        if(isset($_GET['payment_id'])!='' && isset($_GET['payment_request_id'])!='')
        {
        

            $ch = curl_init();
            $url = $this->endpoint.$_GET['payment_request_id'];
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HEADER, FALSE);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, TRUE);
            curl_setopt($ch, CURLOPT_HTTPHEADER,
                        array("X-Api-Key:".$api_key,
                              "X-Auth-Token:".$auth_token));
            $response = curl_exec($ch);
            curl_close($ch);

        $result = json_decode($response);//$response$this->paymentRequestStatus($_GET['payment_request_id']);
        sleep(2);
        //print_r($result);
        // "Payment Successully Done!";
        //$data['payment_requests']=$_GET['payment_requests'];
            if($_GET['payment_status']!='Failed'){
            $data['payment_requests']['success']=$result->payment_request->payments[0]->status;
            $data['payment_requests']['mode']='online';
            $data['payment_requests']['trans_id']=$result->payment_request->id;
            $data['payment_requests']['payment_id']=$result->payment_request->payments[0]->payment_id;
            $data['payment_requests']['purpose']=urldecode($result->payment_request->purpose);
            }
            else{
                $data['payment_requests']['success']='Pending';
                $data['payment_requests']['mode']='online';
                $data['payment_requests']['trans_id']=$_GET['payment_request_id'];
                $data['payment_requests']['payment_id']=$_GET['payment_id'];
                $data['payment_requests']['purpose']=urldecode($result->payment_request->purpose);
                }
        }   
        else if(isset($_GET['mode'])!='' && isset($_GET['purpose'])!='')
        {

        $data['payment_requests']['success']='Pending';
        $data['payment_requests']['mode']=$_GET['mode'];
        $data['payment_requests']['trans_id']='-';
        $data['payment_requests']['payment_id']='-';
        $data['payment_requests']['purpose']=urldecode($_GET['purpose']);
        $data['payment_requests']['events_id']=$_GET['events_id'];
        $data['payment_requests']['user_id']=$this->session->userdata('user_id');
        }
        else
        {
            show_404();

        }
            $payment_update_dtls=$this->events_model->ticketPaymentUpdate($data['payment_requests']);
		
            $data['title'] = 'Payment '.$data['payment_requests']['success'].'!';
            $e_id=$payment_update_dtls[0]['payment_event_id'];
            $data['events_id']=$e_id;
            $data['participants_id']=$this->session->userdata('user_id');
            $data['participants'] = $this->events_model->getSingleParticipants($this->session->userdata('user_id'));
            $data['events'] = $this->events_model->getSingleTicketEvents($e_id);
			
			
			
			$p_id = $this->session->userdata('user_id');
		    $data['eventPaymentRegInfo'] = $this->events_model->getTicketEventPaymentRegInfo($e_id,$p_id);
			
				
            $data['payment_status'] = $data['payment_requests']['success'];
            $data['eventParticipants'] = $this->events_model->getAllParticipantsbyTicketEventSuccessPayment($e_id);
 			// $data['checkPaymentWithUPI'] = $this->events_model->checkPaymentWithUPI($e_id,$p_id);
			
			////////////////////////////////////////////////////////////////////
			$data['e_id'] = $e_id;
        //    $p_id = $this->session->userdata('user_id');
            $data['checkPaymentWithUPI'] = $this->events_model->checkTicketPaymentWithUPI($e_id,$p_id);
			
		
			/////////////////////////////////////////////////////////////////////
 
            // if($data['events']==NULL)
            // $data['events']['categories']= array();
            $data['participants_categories'] = $this->events_model->getTicketParticipantsCategories($this->session->userdata('user_id'),$e_id);
            $data['confirm_status'] = 1;
            $data['payments'] = $this->events_model->getTicketPaymantsdetails($this->session->userdata('user_id'),$e_id);
				
            $data['event_status'] = $data['participants_categories']['events_details']['status'];
            $this->template->write('title', 'Payment '.$data['payment_requests']['success'].'!', TRUE);  
            $data['eventPaymentRegInfo'] = $this->events_model->getTicketEventPaymentRegInfo($e_id,$this->session->userdata('user_id'));

            $this->template->add_css('assets/css/style2.css');
            $this->template->add_css('assets/css/select2.min.css');
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');

            //$this->template->add_js('assets/js/bootstrap-select.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');   
            $this->template->add_js('assets/js/select2.min.js');
			
			$data['eventType']="Ticket Event";
            if(isset($_GET['mode'])!='' && isset($_GET['purpose'])!=''){
			
                $data['event_entry_details'] = $this->events_model->getTicketEventEntryDetails($data['participants_id'],$e_id);
                //print_r($data['event_entry_details']);
                if($data['event_entry_details'][0]['upi_details_transaction_id'] == '')
				{
				    $data['advtDetails']=$this->adds_model->addsList(32);
                    $this->template->write_view('content', 'templates/pages/Events/ticket-upi-payment', $data, TRUE);
				}
                else
				{
					$data['carPaymentProcess']=$this->adds_model->addsList(27);
                    $this->template->write_view('content', 'templates/pages/Events/ticket-payment-process', $data, TRUE);
				}
            }
            else
				{ 
					$data['advtDetails']=$this->adds_model->addsList(34);
              		$this->template->write_view('content', 'templates/pages/Events/payment-confirm', $data, TRUE);
				}
            $this->template->render();            

    } 
	
	
	
	
	
	public function ticketPaymentProcess($e_id)
    {
        if($this->session->userdata('user_id') == NULL)
        {
            redirect(base_url('login'));
            
        }
        else
        {
			$data['ticketPaymentProcess']=$this->adds_model->addsList(27);
            $this->form_validation->set_rules('upiTransactionId', 'UPI Transaction Id', 'required');
            //$this->form_validation->set_rules('category_cnt', 'Category Count', 'required');
            $data['e_id'] = $e_id;
            $p_id = $this->session->userdata('user_id');
            $data['checkPaymentWithUPI'] = $this->events_model->checkTicketPaymentWithUPI($e_id,$p_id);
            $data['events'] = $this->events_model->getSingleTicketEvents($e_id);
            $data['eventPaymentRegInfo'] = $this->events_model->getTicketEventPaymentRegInfo($e_id,$p_id);
            $data['eventParticipants'] = $this->events_model->getAllParticipantsbyTicketEventSuccessPayment($e_id);
			$data['eventPendingParticipants'] = $this->events_model->getAllParticipantsbyTicketEventPendingPayment($e_id);
			
			
			$data['participants_id']=$this->session->userdata('user_id');
			$data['event_entry_details'] = $this->events_model->getTicketEventEntryDetails($data['participants_id'],$e_id);
			
			

            if ($this->form_validation->run() == false) {

                $this->session->set_flashdata('upiTransactionError', 'Error in UPI Transaction Id submission.Please fill the mandatory fields.');
            }
            else {           
                $data['updatePaymentWithUPI'] = $this->events_model->updateTicketPaymentWithUPI();
                $path = 'events/ticket-payment-process/'.$e_id;
                redirect(base_url($path));
            }
            $data['title'] = 'Payment Process';
            $this->template->write('title', 'Payment Process', TRUE);  
            $this->template->add_css('assets/css/style2.css');
            $this->template->add_js('assets/js/bootstrap.bundle.min.js');
            $this->template->add_js('assets/js/form/jquery-1.9.1.min.js');
            $this->template->add_js('assets/js/form/jquery.easing.min.js');
            $this->template->add_js('assets/js/form/script.js');
            $this->template->write_view('content', 'templates/pages/Events/ticket-payment-process', $data, TRUE);
            $this->template->render();
       
        }
    }
}
?>